/**
 * 云边协调编排模块
 * 整合资源调度、服务发现、负载均衡等核心功能
 */

import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { OrchestrationController } from './orchestration.controller';
import { ResourceSchedulerService } from './resource-scheduler.service';
import { ServiceDiscoveryService } from './service-discovery.service';
import { LoadBalancerService } from './load-balancer.service';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
  ],
  controllers: [OrchestrationController],
  providers: [
    ResourceSchedulerService,
    ServiceDiscoveryService,
    LoadBalancerService,
  ],
  exports: [
    ResourceSchedulerService,
    ServiceDiscoveryService,
    LoadBalancerService,
  ],
})
export class OrchestrationModule {}
