/**
 * 版本控制服务
 * 提供项目版本管理、分支管理、合并等功能
 */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

/**
 * 版本实体接口
 */
export interface ProjectVersion {
  id: string;
  projectId: string;
  versionNumber: string;
  branchName: string;
  parentVersionId?: string;
  authorId: string;
  authorName: string;
  message: string;
  changes: VersionChange[];
  metadata: VersionMetadata;
  createdAt: Date;
  tags: string[];
  status: VersionStatus;
}

/**
 * 版本状态枚举
 */
export enum VersionStatus {
  DRAFT = 'draft',
  COMMITTED = 'committed',
  MERGED = 'merged',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

/**
 * 版本变更接口
 */
export interface VersionChange {
  id: string;
  type: ChangeType;
  target: string; // 文件路径或节点ID
  targetType: 'file' | 'node' | 'component' | 'scene' | 'asset';
  operation: ChangeOperation;
  oldValue?: any;
  newValue?: any;
  diff?: string;
  metadata: ChangeMetadata;
}

/**
 * 变更类型枚举
 */
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  RENAME = 'rename',
  COPY = 'copy'
}

/**
 * 变更操作接口
 */
export interface ChangeOperation {
  action: string;
  parameters: any;
  position?: {
    line?: number;
    column?: number;
    offset?: number;
    path?: string;
  };
}

/**
 * 变更元数据接口
 */
export interface ChangeMetadata {
  size: number; // 变更大小（字节）
  complexity: number; // 变更复杂度（1-10）
  impact: 'low' | 'medium' | 'high'; // 影响程度
  category: string; // 变更类别
  reviewRequired: boolean;
  testRequired: boolean;
}

/**
 * 版本元数据接口
 */
export interface VersionMetadata {
  totalChanges: number;
  addedLines: number;
  deletedLines: number;
  modifiedFiles: number;
  buildStatus?: BuildStatus;
  testResults?: TestResults;
  reviewStatus?: ReviewStatus;
  deploymentInfo?: DeploymentInfo;
}

/**
 * 构建状态接口
 */
export interface BuildStatus {
  status: 'pending' | 'building' | 'success' | 'failed';
  buildId?: string;
  startTime?: Date;
  endTime?: Date;
  logs?: string[];
  artifacts?: string[];
}

/**
 * 测试结果接口
 */
export interface TestResults {
  status: 'pending' | 'running' | 'passed' | 'failed';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  coverage: number;
  reports?: string[];
}

/**
 * 审查状态接口
 */
export interface ReviewStatus {
  status: 'pending' | 'in_review' | 'approved' | 'rejected';
  reviewers: string[];
  approvals: number;
  rejections: number;
  comments: ReviewComment[];
}

/**
 * 审查评论接口
 */
export interface ReviewComment {
  id: string;
  reviewerId: string;
  reviewerName: string;
  content: string;
  type: 'general' | 'suggestion' | 'issue' | 'approval';
  target?: string;
  position?: any;
  createdAt: Date;
  resolved: boolean;
}

/**
 * 部署信息接口
 */
export interface DeploymentInfo {
  environment: string;
  deployedAt?: Date;
  deployedBy?: string;
  status: 'pending' | 'deploying' | 'deployed' | 'failed' | 'rolled_back';
  url?: string;
  logs?: string[];
}

/**
 * 分支接口
 */
export interface ProjectBranch {
  name: string;
  projectId: string;
  baseVersionId: string;
  headVersionId: string;
  createdBy: string;
  createdAt: Date;
  description: string;
  status: BranchStatus;
  mergeStatus?: MergeStatus;
  protection: BranchProtection;
}

/**
 * 分支状态枚举
 */
export enum BranchStatus {
  ACTIVE = 'active',
  MERGED = 'merged',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

/**
 * 合并状态接口
 */
export interface MergeStatus {
  canMerge: boolean;
  conflicts: MergeConflict[];
  strategy: MergeStrategy;
  targetBranch: string;
  requestedBy?: string;
  requestedAt?: Date;
}

/**
 * 合并冲突接口
 */
export interface MergeConflict {
  id: string;
  target: string;
  type: 'content' | 'structure' | 'metadata';
  sourceChange: VersionChange;
  targetChange: VersionChange;
  resolution?: ConflictResolution;
}

/**
 * 冲突解决接口
 */
export interface ConflictResolution {
  strategy: 'accept_source' | 'accept_target' | 'merge' | 'manual';
  resolvedValue?: any;
  resolvedBy?: string;
  resolvedAt?: Date;
}

/**
 * 合并策略枚举
 */
export enum MergeStrategy {
  MERGE_COMMIT = 'merge_commit',
  SQUASH = 'squash',
  REBASE = 'rebase',
  FAST_FORWARD = 'fast_forward'
}

/**
 * 分支保护接口
 */
export interface BranchProtection {
  enabled: boolean;
  requireReview: boolean;
  requiredReviewers: number;
  requireTests: boolean;
  requireBuild: boolean;
  allowForcePush: boolean;
  restrictPush: string[]; // 允许推送的用户ID列表
}

/**
 * 标签接口
 */
export interface ProjectTag {
  name: string;
  projectId: string;
  versionId: string;
  type: 'release' | 'milestone' | 'hotfix' | 'custom';
  description: string;
  createdBy: string;
  createdAt: Date;
  metadata: any;
}

@Injectable()
export class VersionControlService {
  private readonly logger = new Logger(VersionControlService.name);
  
  // 内存存储（实际应该使用数据库）
  private versions = new Map<string, ProjectVersion>();
  private branches = new Map<string, ProjectBranch>();
  private tags = new Map<string, ProjectTag>();
  
  // 版本计数器
  private versionCounters = new Map<string, number>();

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeDefaultBranches();
  }

  /**
   * 创建版本
   */
  public async createVersion(
    projectId: string,
    authorId: string,
    authorName: string,
    message: string,
    changes: VersionChange[],
    branchName: string = 'main'
  ): Promise<ProjectVersion> {
    try {
      const versionId = uuidv4();
      const versionNumber = this.generateVersionNumber(projectId);
      
      // 计算版本元数据
      const metadata = this.calculateVersionMetadata(changes);
      
      const version: ProjectVersion = {
        id: versionId,
        projectId,
        versionNumber,
        branchName,
        authorId,
        authorName,
        message,
        changes,
        metadata,
        createdAt: new Date(),
        tags: [],
        status: VersionStatus.COMMITTED
      };
      
      // 设置父版本
      const branch = this.getBranch(projectId, branchName);
      if (branch) {
        version.parentVersionId = branch.headVersionId;
        // 更新分支头部版本
        branch.headVersionId = versionId;
      }
      
      // 保存版本
      this.versions.set(versionId, version);
      
      // 发送事件
      this.eventEmitter.emit('version.created', {
        version,
        projectId,
        branchName
      });
      
      this.logger.log(`版本已创建: ${versionNumber} by ${authorName}`);
      return version;
      
    } catch (error) {
      this.logger.error('创建版本失败:', error);
      throw error;
    }
  }

  /**
   * 获取版本历史
   */
  public getVersionHistory(
    projectId: string, 
    branchName?: string, 
    limit: number = 50
  ): ProjectVersion[] {
    let versions = Array.from(this.versions.values())
      .filter(v => v.projectId === projectId);
    
    if (branchName) {
      versions = versions.filter(v => v.branchName === branchName);
    }
    
    return versions
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  /**
   * 获取版本详情
   */
  public getVersion(versionId: string): ProjectVersion | null {
    return this.versions.get(versionId) || null;
  }

  /**
   * 创建分支
   */
  public async createBranch(
    projectId: string,
    branchName: string,
    baseVersionId: string,
    createdBy: string,
    description: string = ''
  ): Promise<ProjectBranch> {
    try {
      // 检查分支是否已存在
      const existingBranch = this.getBranch(projectId, branchName);
      if (existingBranch) {
        throw new Error(`分支 ${branchName} 已存在`);
      }
      
      // 验证基础版本
      const baseVersion = this.versions.get(baseVersionId);
      if (!baseVersion) {
        throw new Error('基础版本不存在');
      }
      
      const branch: ProjectBranch = {
        name: branchName,
        projectId,
        baseVersionId,
        headVersionId: baseVersionId,
        createdBy,
        createdAt: new Date(),
        description,
        status: BranchStatus.ACTIVE,
        protection: {
          enabled: false,
          requireReview: false,
          requiredReviewers: 0,
          requireTests: false,
          requireBuild: false,
          allowForcePush: true,
          restrictPush: []
        }
      };
      
      const branchKey = `${projectId}:${branchName}`;
      this.branches.set(branchKey, branch);
      
      // 发送事件
      this.eventEmitter.emit('branch.created', {
        branch,
        projectId,
        baseVersion
      });
      
      this.logger.log(`分支已创建: ${branchName} from ${baseVersion.versionNumber}`);
      return branch;
      
    } catch (error) {
      this.logger.error('创建分支失败:', error);
      throw error;
    }
  }

  /**
   * 获取分支列表
   */
  public getBranches(projectId: string): ProjectBranch[] {
    return Array.from(this.branches.values())
      .filter(b => b.projectId === projectId && b.status === BranchStatus.ACTIVE);
  }

  /**
   * 获取分支
   */
  public getBranch(projectId: string, branchName: string): ProjectBranch | null {
    const branchKey = `${projectId}:${branchName}`;
    return this.branches.get(branchKey) || null;
  }

  /**
   * 合并分支
   */
  public async mergeBranch(
    projectId: string,
    sourceBranch: string,
    targetBranch: string,
    strategy: MergeStrategy = MergeStrategy.MERGE_COMMIT,
    mergedBy: string
  ): Promise<ProjectVersion> {
    try {
      const source = this.getBranch(projectId, sourceBranch);
      const target = this.getBranch(projectId, targetBranch);
      
      if (!source || !target) {
        throw new Error('源分支或目标分支不存在');
      }
      
      // 检查合并冲突
      const conflicts = await this.detectMergeConflicts(source, target);
      
      if (conflicts.length > 0) {
        throw new Error(`存在 ${conflicts.length} 个合并冲突，请先解决冲突`);
      }
      
      // 获取源分支的变更
      const sourceChanges = await this.getBranchChanges(source);
      
      // 创建合并版本
      const mergeVersion = await this.createVersion(
        projectId,
        mergedBy,
        'System',
        `Merge branch '${sourceBranch}' into '${targetBranch}'`,
        sourceChanges,
        targetBranch
      );
      
      mergeVersion.status = VersionStatus.MERGED;
      
      // 更新分支状态
      if (strategy === MergeStrategy.SQUASH) {
        source.status = BranchStatus.MERGED;
      }
      
      // 发送事件
      this.eventEmitter.emit('branch.merged', {
        sourceBranch: source,
        targetBranch: target,
        mergeVersion,
        strategy,
        conflicts: conflicts.length
      });
      
      this.logger.log(`分支已合并: ${sourceBranch} -> ${targetBranch}`);
      return mergeVersion;
      
    } catch (error) {
      this.logger.error('合并分支失败:', error);
      throw error;
    }
  }

  /**
   * 创建标签
   */
  public async createTag(
    projectId: string,
    versionId: string,
    tagName: string,
    type: ProjectTag['type'],
    description: string,
    createdBy: string,
    metadata: any = {}
  ): Promise<ProjectTag> {
    try {
      // 检查标签是否已存在
      const existingTag = this.getTag(projectId, tagName);
      if (existingTag) {
        throw new Error(`标签 ${tagName} 已存在`);
      }
      
      // 验证版本
      const version = this.versions.get(versionId);
      if (!version) {
        throw new Error('版本不存在');
      }
      
      const tag: ProjectTag = {
        name: tagName,
        projectId,
        versionId,
        type,
        description,
        createdBy,
        createdAt: new Date(),
        metadata
      };
      
      const tagKey = `${projectId}:${tagName}`;
      this.tags.set(tagKey, tag);
      
      // 添加标签到版本
      version.tags.push(tagName);
      
      // 发送事件
      this.eventEmitter.emit('tag.created', {
        tag,
        version,
        projectId
      });
      
      this.logger.log(`标签已创建: ${tagName} on ${version.versionNumber}`);
      return tag;
      
    } catch (error) {
      this.logger.error('创建标签失败:', error);
      throw error;
    }
  }

  /**
   * 获取标签
   */
  public getTag(projectId: string, tagName: string): ProjectTag | null {
    const tagKey = `${projectId}:${tagName}`;
    return this.tags.get(tagKey) || null;
  }

  /**
   * 获取项目标签列表
   */
  public getTags(projectId: string): ProjectTag[] {
    return Array.from(this.tags.values())
      .filter(t => t.projectId === projectId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 比较版本
   */
  public async compareVersions(
    versionId1: string, 
    versionId2: string
  ): Promise<{
    added: VersionChange[];
    modified: VersionChange[];
    deleted: VersionChange[];
    summary: {
      totalChanges: number;
      addedLines: number;
      deletedLines: number;
      modifiedFiles: number;
    };
  }> {
    const version1 = this.versions.get(versionId1);
    const version2 = this.versions.get(versionId2);
    
    if (!version1 || !version2) {
      throw new Error('版本不存在');
    }
    
    // 简化的比较实现
    const changes1 = new Map(version1.changes.map(c => [c.target, c]));
    const changes2 = new Map(version2.changes.map(c => [c.target, c]));
    
    const added: VersionChange[] = [];
    const modified: VersionChange[] = [];
    const deleted: VersionChange[] = [];
    
    // 查找新增和修改
    for (const [target, change2] of changes2) {
      const change1 = changes1.get(target);
      if (!change1) {
        added.push(change2);
      } else if (JSON.stringify(change1.newValue) !== JSON.stringify(change2.newValue)) {
        modified.push(change2);
      }
    }
    
    // 查找删除
    for (const [target, change1] of changes1) {
      if (!changes2.has(target)) {
        deleted.push(change1);
      }
    }
    
    const summary = {
      totalChanges: added.length + modified.length + deleted.length,
      addedLines: added.reduce((sum, c) => sum + (c.metadata.size || 0), 0),
      deletedLines: deleted.reduce((sum, c) => sum + (c.metadata.size || 0), 0),
      modifiedFiles: modified.length
    };
    
    return { added, modified, deleted, summary };
  }

  // ==================== 私有方法 ====================

  /**
   * 生成版本号
   */
  private generateVersionNumber(projectId: string): string {
    const counter = this.versionCounters.get(projectId) || 0;
    const newCounter = counter + 1;
    this.versionCounters.set(projectId, newCounter);
    
    // 生成语义化版本号
    const major = Math.floor(newCounter / 1000);
    const minor = Math.floor((newCounter % 1000) / 100);
    const patch = newCounter % 100;
    
    return `${major}.${minor}.${patch}`;
  }

  /**
   * 计算版本元数据
   */
  private calculateVersionMetadata(changes: VersionChange[]): VersionMetadata {
    const addedLines = changes
      .filter(c => c.type === ChangeType.CREATE || c.type === ChangeType.UPDATE)
      .reduce((sum, c) => sum + (c.metadata.size || 0), 0);
    
    const deletedLines = changes
      .filter(c => c.type === ChangeType.DELETE)
      .reduce((sum, c) => sum + (c.metadata.size || 0), 0);
    
    const modifiedFiles = new Set(changes.map(c => c.target)).size;
    
    return {
      totalChanges: changes.length,
      addedLines,
      deletedLines,
      modifiedFiles
    };
  }

  /**
   * 检测合并冲突
   */
  private async detectMergeConflicts(
    sourceBranch: ProjectBranch, 
    targetBranch: ProjectBranch
  ): Promise<MergeConflict[]> {
    // 简化的冲突检测实现
    const conflicts: MergeConflict[] = [];
    
    const sourceChanges = await this.getBranchChanges(sourceBranch);
    const targetChanges = await this.getBranchChanges(targetBranch);
    
    // 检查同一目标的并发修改
    const sourceTargets = new Map(sourceChanges.map(c => [c.target, c]));
    const targetTargets = new Map(targetChanges.map(c => [c.target, c]));
    
    for (const [target, sourceChange] of sourceTargets) {
      const targetChange = targetTargets.get(target);
      if (targetChange && 
          JSON.stringify(sourceChange.newValue) !== JSON.stringify(targetChange.newValue)) {
        conflicts.push({
          id: uuidv4(),
          target,
          type: 'content',
          sourceChange,
          targetChange
        });
      }
    }
    
    return conflicts;
  }

  /**
   * 获取分支变更
   */
  private async getBranchChanges(branch: ProjectBranch): Promise<VersionChange[]> {
    const changes: VersionChange[] = [];
    
    // 从基础版本到头部版本的所有变更
    let currentVersionId = branch.headVersionId;
    
    while (currentVersionId && currentVersionId !== branch.baseVersionId) {
      const version = this.versions.get(currentVersionId);
      if (!version) break;
      
      changes.unshift(...version.changes);
      currentVersionId = version.parentVersionId;
    }
    
    return changes;
  }

  /**
   * 初始化默认分支
   */
  private initializeDefaultBranches(): void {
    // 这里可以为每个项目创建默认的main分支
    // 实际实现中应该在项目创建时调用
  }
}
