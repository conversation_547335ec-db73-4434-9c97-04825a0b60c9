/**
 * 版本控制控制器
 * 提供版本管理、分支管理、合并等API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  VersionControlService, 
  ProjectVersion, 
  ProjectBranch,
  ProjectTag,
  MergeStrategy 
} from './version-control.service';

@ApiTags('版本控制')
@Controller('api/v1/projects/:projectId/version-control')
export class VersionControlController {
  private readonly logger = new Logger(VersionControlController.name);

  constructor(private readonly versionControlService: VersionControlService) {}

  /**
   * 创建版本
   */
  @Post('versions')
  @ApiOperation({ summary: '创建新版本' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '版本创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createVersion(
    @Param('projectId') projectId: string,
    @Body() body: {
      authorId: string;
      authorName: string;
      message: string;
      changes: any[];
      branchName?: string;
    }
  ): Promise<ProjectVersion> {
    try {
      this.logger.log(`创建版本: ${projectId} by ${body.authorName}`);
      
      const version = await this.versionControlService.createVersion(
        projectId,
        body.authorId,
        body.authorName,
        body.message,
        body.changes,
        body.branchName
      );
      
      this.logger.log(`版本创建成功: ${version.versionNumber}`);
      return version;

    } catch (error) {
      this.logger.error(`创建版本失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取版本历史
   */
  @Get('versions')
  @ApiOperation({ summary: '获取版本历史' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiQuery({ name: 'branchName', required: false, description: '分支名称' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getVersionHistory(
    @Param('projectId') projectId: string,
    @Query('branchName') branchName?: string,
    @Query('limit') limit?: string
  ): Promise<ProjectVersion[]> {
    try {
      const limitNum = limit ? parseInt(limit) : 50;
      
      const versions = this.versionControlService.getVersionHistory(
        projectId,
        branchName,
        limitNum
      );
      
      this.logger.log(`获取版本历史: ${projectId}, ${versions.length} 个版本`);
      return versions;

    } catch (error) {
      this.logger.error(`获取版本历史失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取版本详情
   */
  @Get('versions/:versionId')
  @ApiOperation({ summary: '获取版本详情' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'versionId', description: '版本ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '版本不存在' })
  async getVersion(
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string
  ): Promise<ProjectVersion> {
    try {
      const version = this.versionControlService.getVersion(versionId);
      
      if (!version) {
        throw new HttpException('版本不存在', HttpStatus.NOT_FOUND);
      }
      
      this.logger.log(`获取版本详情: ${versionId}`);
      return version;

    } catch (error) {
      this.logger.error(`获取版本详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建分支
   */
  @Post('branches')
  @ApiOperation({ summary: '创建新分支' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '分支创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createBranch(
    @Param('projectId') projectId: string,
    @Body() body: {
      branchName: string;
      baseVersionId: string;
      createdBy: string;
      description?: string;
    }
  ): Promise<ProjectBranch> {
    try {
      this.logger.log(`创建分支: ${body.branchName} from ${body.baseVersionId}`);
      
      const branch = await this.versionControlService.createBranch(
        projectId,
        body.branchName,
        body.baseVersionId,
        body.createdBy,
        body.description
      );
      
      this.logger.log(`分支创建成功: ${branch.name}`);
      return branch;

    } catch (error) {
      this.logger.error(`创建分支失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取分支列表
   */
  @Get('branches')
  @ApiOperation({ summary: '获取分支列表' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getBranches(
    @Param('projectId') projectId: string
  ): Promise<ProjectBranch[]> {
    try {
      const branches = this.versionControlService.getBranches(projectId);
      
      this.logger.log(`获取分支列表: ${projectId}, ${branches.length} 个分支`);
      return branches;

    } catch (error) {
      this.logger.error(`获取分支列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取分支详情
   */
  @Get('branches/:branchName')
  @ApiOperation({ summary: '获取分支详情' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'branchName', description: '分支名称' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '分支不存在' })
  async getBranch(
    @Param('projectId') projectId: string,
    @Param('branchName') branchName: string
  ): Promise<ProjectBranch> {
    try {
      const branch = this.versionControlService.getBranch(projectId, branchName);
      
      if (!branch) {
        throw new HttpException('分支不存在', HttpStatus.NOT_FOUND);
      }
      
      this.logger.log(`获取分支详情: ${branchName}`);
      return branch;

    } catch (error) {
      this.logger.error(`获取分支详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 合并分支
   */
  @Post('branches/:sourceBranch/merge')
  @ApiOperation({ summary: '合并分支' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'sourceBranch', description: '源分支名称' })
  @ApiResponse({ status: 201, description: '合并成功' })
  @ApiResponse({ status: 409, description: '存在合并冲突' })
  async mergeBranch(
    @Param('projectId') projectId: string,
    @Param('sourceBranch') sourceBranch: string,
    @Body() body: {
      targetBranch: string;
      strategy?: MergeStrategy;
      mergedBy: string;
    }
  ): Promise<ProjectVersion> {
    try {
      this.logger.log(`合并分支: ${sourceBranch} -> ${body.targetBranch}`);
      
      const mergeVersion = await this.versionControlService.mergeBranch(
        projectId,
        sourceBranch,
        body.targetBranch,
        body.strategy,
        body.mergedBy
      );
      
      this.logger.log(`分支合并成功: ${mergeVersion.versionNumber}`);
      return mergeVersion;

    } catch (error) {
      this.logger.error(`合并分支失败: ${error.message}`);
      if (error.message.includes('冲突')) {
        throw new HttpException(error.message, HttpStatus.CONFLICT);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 创建标签
   */
  @Post('tags')
  @ApiOperation({ summary: '创建标签' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '标签创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createTag(
    @Param('projectId') projectId: string,
    @Body() body: {
      versionId: string;
      tagName: string;
      type: string;
      description: string;
      createdBy: string;
      metadata?: any;
    }
  ): Promise<ProjectTag> {
    try {
      this.logger.log(`创建标签: ${body.tagName} on ${body.versionId}`);
      
      const tag = await this.versionControlService.createTag(
        projectId,
        body.versionId,
        body.tagName,
        body.type as any,
        body.description,
        body.createdBy,
        body.metadata
      );
      
      this.logger.log(`标签创建成功: ${tag.name}`);
      return tag;

    } catch (error) {
      this.logger.error(`创建标签失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取标签列表
   */
  @Get('tags')
  @ApiOperation({ summary: '获取标签列表' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTags(
    @Param('projectId') projectId: string
  ): Promise<ProjectTag[]> {
    try {
      const tags = this.versionControlService.getTags(projectId);
      
      this.logger.log(`获取标签列表: ${projectId}, ${tags.length} 个标签`);
      return tags;

    } catch (error) {
      this.logger.error(`获取标签列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取标签详情
   */
  @Get('tags/:tagName')
  @ApiOperation({ summary: '获取标签详情' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'tagName', description: '标签名称' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '标签不存在' })
  async getTag(
    @Param('projectId') projectId: string,
    @Param('tagName') tagName: string
  ): Promise<ProjectTag> {
    try {
      const tag = this.versionControlService.getTag(projectId, tagName);
      
      if (!tag) {
        throw new HttpException('标签不存在', HttpStatus.NOT_FOUND);
      }
      
      this.logger.log(`获取标签详情: ${tagName}`);
      return tag;

    } catch (error) {
      this.logger.error(`获取标签详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 比较版本
   */
  @Get('versions/:versionId1/compare/:versionId2')
  @ApiOperation({ summary: '比较两个版本' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'versionId1', description: '版本1 ID' })
  @ApiParam({ name: 'versionId2', description: '版本2 ID' })
  @ApiResponse({ status: 200, description: '比较成功' })
  @ApiResponse({ status: 404, description: '版本不存在' })
  async compareVersions(
    @Param('projectId') projectId: string,
    @Param('versionId1') versionId1: string,
    @Param('versionId2') versionId2: string
  ): Promise<any> {
    try {
      this.logger.log(`比较版本: ${versionId1} vs ${versionId2}`);
      
      const comparison = await this.versionControlService.compareVersions(
        versionId1,
        versionId2
      );
      
      this.logger.log(`版本比较完成: ${comparison.summary.totalChanges} 个变更`);
      return comparison;

    } catch (error) {
      this.logger.error(`比较版本失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
