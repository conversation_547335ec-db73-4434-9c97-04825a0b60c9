import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { RoomService } from './room.service';
import {
  CreateRoomParams,
  JoinRoomParams,
  RoomSearchParams,
  GameRoom,
  RoomStatus,
  RoomType
} from './room.interface';

@ApiTags('房间管理')
@Controller('rooms')
export class RoomController {
  private readonly logger = new Logger(RoomController.name);

  constructor(private readonly roomService: RoomService) {}

  /**
   * 创建房间
   */
  @Post()
  @ApiOperation({ summary: '创建游戏房间' })
  @ApiResponse({ status: 201, description: '房间创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createRoom(@Body() createRoomDto: CreateRoomParams): Promise<{
    success: boolean;
    data: GameRoom;
    message: string;
  }> {
    try {
      const room = await this.roomService.createRoom(createRoomDto);
      
      return {
        success: true,
        data: room,
        message: '房间创建成功'
      };
    } catch (error) {
      this.logger.error(`创建房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 加入房间
   */
  @Post(':roomId/join')
  @ApiOperation({ summary: '加入游戏房间' })
  @ApiParam({ name: 'roomId', description: '房间ID' })
  @ApiResponse({ status: 200, description: '加入房间成功' })
  @ApiResponse({ status: 404, description: '房间不存在' })
  @ApiResponse({ status: 400, description: '无法加入房间' })
  async joinRoom(
    @Param('roomId') roomId: string,
    @Body() joinRoomDto: Omit<JoinRoomParams, 'roomId'>
  ): Promise<{
    success: boolean;
    data: GameRoom;
    message: string;
  }> {
    try {
      const room = await this.roomService.joinRoom({
        ...joinRoomDto,
        roomId
      });
      
      return {
        success: true,
        data: room,
        message: '加入房间成功'
      };
    } catch (error) {
      this.logger.error(`加入房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 离开房间
   */
  @Delete(':roomId/leave/:playerId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '离开游戏房间' })
  @ApiParam({ name: 'roomId', description: '房间ID' })
  @ApiParam({ name: 'playerId', description: '玩家ID' })
  @ApiResponse({ status: 204, description: '离开房间成功' })
  async leaveRoom(
    @Param('roomId') roomId: string,
    @Param('playerId') playerId: string
  ): Promise<void> {
    try {
      await this.roomService.leaveRoom(playerId);
      this.logger.log(`玩家 ${playerId} 离开房间 ${roomId}`);
    } catch (error) {
      this.logger.error(`离开房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 开始游戏
   */
  @Put(':roomId/start')
  @ApiOperation({ summary: '开始游戏' })
  @ApiParam({ name: 'roomId', description: '房间ID' })
  @ApiResponse({ status: 200, description: '游戏开始成功' })
  @ApiResponse({ status: 404, description: '房间不存在' })
  @ApiResponse({ status: 400, description: '无法开始游戏' })
  async startGame(@Param('roomId') roomId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      await this.roomService.startGame(roomId);
      
      return {
        success: true,
        message: '游戏开始成功'
      };
    } catch (error) {
      this.logger.error(`开始游戏失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 关闭房间
   */
  @Delete(':roomId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '关闭游戏房间' })
  @ApiParam({ name: 'roomId', description: '房间ID' })
  @ApiResponse({ status: 204, description: '房间关闭成功' })
  async closeRoom(@Param('roomId') roomId: string): Promise<void> {
    try {
      await this.roomService.closeRoom(roomId);
      this.logger.log(`房间 ${roomId} 已关闭`);
    } catch (error) {
      this.logger.error(`关闭房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取房间信息
   */
  @Get(':roomId')
  @ApiOperation({ summary: '获取房间信息' })
  @ApiParam({ name: 'roomId', description: '房间ID' })
  @ApiResponse({ status: 200, description: '获取房间信息成功' })
  @ApiResponse({ status: 404, description: '房间不存在' })
  async getRoom(@Param('roomId') roomId: string): Promise<{
    success: boolean;
    data: GameRoom;
  }> {
    try {
      const room = this.roomService.getRoom(roomId);
      
      if (!room) {
        throw new Error('房间不存在');
      }
      
      return {
        success: true,
        data: room
      };
    } catch (error) {
      this.logger.error(`获取房间信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 搜索房间
   */
  @Get()
  @ApiOperation({ summary: '搜索游戏房间' })
  @ApiQuery({ name: 'gameType', required: false, description: '游戏类型' })
  @ApiQuery({ name: 'gameMode', required: false, description: '游戏模式' })
  @ApiQuery({ name: 'status', required: false, enum: RoomStatus, description: '房间状态' })
  @ApiQuery({ name: 'type', required: false, enum: RoomType, description: '房间类型' })
  @ApiQuery({ name: 'hasSlots', required: false, type: Boolean, description: '是否有空位' })
  @ApiQuery({ name: 'region', required: false, description: '地区' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '返回数量限制' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: '偏移量' })
  @ApiResponse({ status: 200, description: '搜索房间成功' })
  async searchRooms(@Query() searchParams: RoomSearchParams): Promise<{
    success: boolean;
    data: GameRoom[];
    total: number;
  }> {
    try {
      const rooms = this.roomService.searchRooms(searchParams);
      
      return {
        success: true,
        data: rooms,
        total: rooms.length
      };
    } catch (error) {
      this.logger.error(`搜索房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取玩家当前房间
   */
  @Get('player/:playerId')
  @ApiOperation({ summary: '获取玩家当前房间' })
  @ApiParam({ name: 'playerId', description: '玩家ID' })
  @ApiResponse({ status: 200, description: '获取玩家房间成功' })
  async getPlayerRoom(@Param('playerId') playerId: string): Promise<{
    success: boolean;
    data: GameRoom | null;
  }> {
    try {
      const room = this.roomService.getPlayerRoom(playerId);
      
      return {
        success: true,
        data: room || null
      };
    } catch (error) {
      this.logger.error(`获取玩家房间失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取房间统计信息
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取房间统计信息' })
  @ApiResponse({ status: 200, description: '获取统计信息成功' })
  async getRoomStats(): Promise<{
    success: boolean;
    data: any;
  }> {
    try {
      const stats = this.roomService.getRoomStats();
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      this.logger.error(`获取房间统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
