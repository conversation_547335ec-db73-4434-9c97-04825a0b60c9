/**
 * 游戏房间状态枚举
 */
export enum RoomStatus {
  WAITING = 'waiting',
  STARTING = 'starting',
  PLAYING = 'playing',
  PAUSED = 'paused',
  FINISHED = 'finished',
  CLOSED = 'closed'
}

/**
 * 玩家状态枚举
 */
export enum PlayerStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  PLAYING = 'playing',
  SPECTATING = 'spectating',
  KICKED = 'kicked'
}

/**
 * 房间类型枚举
 */
export enum RoomType {
  PUBLIC = 'public',
  PRIVATE = 'private',
  RANKED = 'ranked',
  CUSTOM = 'custom'
}

/**
 * 玩家信息接口
 */
export interface Player {
  id: string;
  userId: string;
  username: string;
  status: PlayerStatus;
  joinedAt: Date;
  lastActiveAt: Date;
  isHost: boolean;
  team?: string;
  score?: number;
  metadata: Record<string, any>;
}

/**
 * 房间配置接口
 */
export interface RoomConfig {
  maxPlayers: number;
  minPlayers: number;
  gameMode: string;
  gameType: string;
  mapId?: string;
  isPrivate: boolean;
  password?: string;
  allowSpectators: boolean;
  maxSpectators: number;
  autoStart: boolean;
  autoStartDelay: number; // 秒
  gameSettings: Record<string, any>;
}

/**
 * 游戏房间接口
 */
export interface GameRoom {
  id: string;
  name: string;
  type: RoomType;
  status: RoomStatus;
  config: RoomConfig;
  players: Map<string, Player>;
  spectators: Map<string, Player>;
  hostId: string;
  instanceId?: string;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  finishedAt?: Date;
  gameState: Record<string, any>;
  metadata: Record<string, any>;
}

/**
 * 房间事件接口
 */
export interface RoomEvent {
  type: string;
  roomId: string;
  playerId?: string;
  data: any;
  timestamp: Date;
}

/**
 * 创建房间参数接口
 */
export interface CreateRoomParams {
  name: string;
  type: RoomType;
  config: Partial<RoomConfig>;
  hostId: string;
  metadata?: Record<string, any>;
}

/**
 * 加入房间参数接口
 */
export interface JoinRoomParams {
  roomId: string;
  playerId: string;
  username: string;
  password?: string;
  asSpectator?: boolean;
  metadata?: Record<string, any>;
}

/**
 * 房间搜索参数接口
 */
export interface RoomSearchParams {
  gameType?: string;
  gameMode?: string;
  status?: RoomStatus;
  type?: RoomType;
  hasSlots?: boolean;
  region?: string;
  limit?: number;
  offset?: number;
}
