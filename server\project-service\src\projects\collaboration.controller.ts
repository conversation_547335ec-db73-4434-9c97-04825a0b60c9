/**
 * 协作控制器
 * 提供实时协作相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  CollaborationService, 
  CollaborationSession, 
  CollaborationOperation,
  CursorPosition,
  SelectionRange,
  CollaborationState 
} from './collaboration.service';

@ApiTags('项目协作')
@Controller('api/v1/projects/:projectId/collaboration')
export class CollaborationController {
  private readonly logger = new Logger(CollaborationController.name);

  constructor(private readonly collaborationService: CollaborationService) {}

  /**
   * 加入协作会话
   */
  @Post('sessions')
  @ApiOperation({ summary: '加入项目协作会话' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '加入成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async joinSession(
    @Param('projectId') projectId: string,
    @Body() body: {
      userId: string;
      userName: string;
      clientInfo: any;
      permissions: any;
    }
  ): Promise<CollaborationSession> {
    try {
      this.logger.log(`用户 ${body.userName} 请求加入项目 ${projectId} 协作会话`);
      
      const session = await this.collaborationService.joinSession(
        projectId,
        body.userId,
        body.userName,
        body.clientInfo,
        body.permissions
      );
      
      this.logger.log(`协作会话已创建: ${session.sessionId}`);
      return session;

    } catch (error) {
      this.logger.error(`加入协作会话失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 离开协作会话
   */
  @Delete('sessions/:sessionId')
  @ApiOperation({ summary: '离开协作会话' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '离开成功' })
  async leaveSession(
    @Param('projectId') projectId: string,
    @Param('sessionId') sessionId: string
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`离开协作会话: ${sessionId}`);
      
      await this.collaborationService.leaveSession(sessionId);
      
      this.logger.log(`已离开协作会话: ${sessionId}`);
      return { message: '已离开协作会话' };

    } catch (error) {
      this.logger.error(`离开协作会话失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 提交协作操作
   */
  @Post('operations')
  @ApiOperation({ summary: '提交协作操作' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '操作提交成功' })
  @ApiResponse({ status: 400, description: '操作无效' })
  async submitOperation(
    @Param('projectId') projectId: string,
    @Body() body: {
      sessionId: string;
      operation: any;
    }
  ): Promise<CollaborationOperation> {
    try {
      this.logger.log(`提交协作操作: ${body.operation.type}`);
      
      const operation = await this.collaborationService.submitOperation(
        body.sessionId,
        { ...body.operation, projectId }
      );
      
      this.logger.log(`协作操作已提交: ${operation.id}`);
      return operation;

    } catch (error) {
      this.logger.error(`提交协作操作失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 更新光标位置
   */
  @Put('sessions/:sessionId/cursor')
  @ApiOperation({ summary: '更新光标位置' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateCursor(
    @Param('projectId') projectId: string,
    @Param('sessionId') sessionId: string,
    @Body() cursor: CursorPosition
  ): Promise<{ message: string }> {
    try {
      await this.collaborationService.updateCursor(sessionId, cursor);
      
      return { message: '光标位置已更新' };

    } catch (error) {
      this.logger.error(`更新光标位置失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 更新选择范围
   */
  @Put('sessions/:sessionId/selection')
  @ApiOperation({ summary: '更新选择范围' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateSelection(
    @Param('projectId') projectId: string,
    @Param('sessionId') sessionId: string,
    @Body() selection: SelectionRange
  ): Promise<{ message: string }> {
    try {
      await this.collaborationService.updateSelection(sessionId, selection);
      
      return { message: '选择范围已更新' };

    } catch (error) {
      this.logger.error(`更新选择范围失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取协作状态
   */
  @Get('state')
  @ApiOperation({ summary: '获取项目协作状态' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getCollaborationState(
    @Param('projectId') projectId: string
  ): Promise<CollaborationState | null> {
    try {
      const state = this.collaborationService.getCollaborationState(projectId);
      
      this.logger.log(`获取协作状态: ${projectId}`);
      return state;

    } catch (error) {
      this.logger.error(`获取协作状态失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取活跃会话
   */
  @Get('sessions')
  @ApiOperation({ summary: '获取活跃协作会话' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getActiveSessions(
    @Param('projectId') projectId: string
  ): Promise<CollaborationSession[]> {
    try {
      const sessions = this.collaborationService.getActiveSessions(projectId);
      
      this.logger.log(`获取活跃会话: ${projectId}, ${sessions.length} 个会话`);
      return sessions;

    } catch (error) {
      this.logger.error(`获取活跃会话失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 锁定资源
   */
  @Post('locks')
  @ApiOperation({ summary: '锁定资源' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiResponse({ status: 201, description: '锁定成功' })
  @ApiResponse({ status: 409, description: '资源已被锁定' })
  async lockResource(
    @Param('projectId') projectId: string,
    @Body() body: {
      sessionId: string;
      resourceId: string;
      resourceType: string;
      lockType?: string;
      reason?: string;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`锁定资源请求: ${body.resourceId}`);
      
      const success = await this.collaborationService.lockResource(
        body.sessionId,
        body.resourceId,
        body.resourceType as any,
        body.lockType as any,
        body.reason
      );
      
      if (success) {
        this.logger.log(`资源锁定成功: ${body.resourceId}`);
        return { success: true, message: '资源已锁定' };
      } else {
        throw new HttpException('资源已被其他用户锁定', HttpStatus.CONFLICT);
      }

    } catch (error) {
      this.logger.error(`锁定资源失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 解锁资源
   */
  @Delete('locks/:resourceId')
  @ApiOperation({ summary: '解锁资源' })
  @ApiParam({ name: 'projectId', description: '项目ID' })
  @ApiParam({ name: 'resourceId', description: '资源ID' })
  @ApiResponse({ status: 200, description: '解锁成功' })
  async unlockResource(
    @Param('projectId') projectId: string,
    @Param('resourceId') resourceId: string,
    @Body() body: { sessionId: string }
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`解锁资源请求: ${resourceId}`);
      
      const success = await this.collaborationService.unlockResource(
        body.sessionId,
        resourceId
      );
      
      if (success) {
        this.logger.log(`资源解锁成功: ${resourceId}`);
        return { success: true, message: '资源已解锁' };
      } else {
        return { success: false, message: '解锁失败，可能没有锁定权限' };
      }

    } catch (error) {
      this.logger.error(`解锁资源失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
