import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RoomService } from './room.service';
import { RoomController } from './room.controller';
import { RoomGateway } from './room.gateway';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule
  ],
  controllers: [RoomController],
  providers: [RoomService, RoomGateway],
  exports: [RoomService]
})
export class RoomModule {}
