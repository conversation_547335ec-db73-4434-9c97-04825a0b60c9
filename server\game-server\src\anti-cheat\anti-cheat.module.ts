import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { AntiCheatService } from './anti-cheat.service';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule
  ],
  providers: [AntiCheatService],
  exports: [AntiCheatService]
})
export class AntiCheatModule {}
