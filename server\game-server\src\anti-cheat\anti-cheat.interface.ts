/**
 * 作弊类型枚举
 */
export enum CheatType {
  SPEED_HACK = 'speed_hack',
  TELEPORT = 'teleport',
  WALL_HACK = 'wall_hack',
  AIM_BOT = 'aim_bot',
  RESOURCE_HACK = 'resource_hack',
  PACKET_MANIPULATION = 'packet_manipulation',
  TIME_MANIPULATION = 'time_manipulation',
  INVALID_ACTION = 'invalid_action',
  SUSPICIOUS_BEHAVIOR = 'suspicious_behavior'
}

/**
 * 检测严重程度枚举
 */
export enum SeverityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 处理动作枚举
 */
export enum ActionType {
  LOG = 'log',
  WARN = 'warn',
  KICK = 'kick',
  BAN_TEMPORARY = 'ban_temporary',
  BAN_PERMANENT = 'ban_permanent',
  RESET_POSITION = 'reset_position',
  REDUCE_PRIVILEGES = 'reduce_privileges'
}

/**
 * 作弊检测结果接口
 */
export interface CheatDetection {
  id: string;
  playerId: string;
  roomId: string;
  type: CheatType;
  severity: SeverityLevel;
  confidence: number; // 0-1之间的置信度
  evidence: CheatEvidence;
  timestamp: Date;
  resolved: boolean;
  action?: ActionType;
  metadata: Record<string, any>;
}

/**
 * 作弊证据接口
 */
export interface CheatEvidence {
  type: string;
  description: string;
  data: any;
  context: {
    playerState: any;
    gameState: any;
    networkInfo: any;
  };
  screenshots?: string[];
  logs?: string[];
}

/**
 * 玩家行为模式接口
 */
export interface PlayerBehaviorPattern {
  playerId: string;
  sessionStart: Date;
  actions: PlayerAction[];
  statistics: BehaviorStatistics;
  anomalies: BehaviorAnomaly[];
  riskScore: number; // 0-100的风险评分
}

/**
 * 玩家动作接口
 */
export interface PlayerAction {
  type: string;
  timestamp: Date;
  position?: { x: number; y: number; z: number };
  data: any;
  latency: number;
  validated: boolean;
}

/**
 * 行为统计接口
 */
export interface BehaviorStatistics {
  totalActions: number;
  actionsPerMinute: number;
  averageLatency: number;
  movementSpeed: {
    average: number;
    maximum: number;
    variance: number;
  };
  accuracy: {
    hitRate: number;
    headShotRate: number;
    reactionTime: number;
  };
  patterns: {
    repeatingSequences: number;
    unusualTimings: number;
    impossibleActions: number;
  };
}

/**
 * 行为异常接口
 */
export interface BehaviorAnomaly {
  type: string;
  description: string;
  severity: SeverityLevel;
  timestamp: Date;
  evidence: any;
  score: number;
}

/**
 * 反作弊配置接口
 */
export interface AntiCheatConfig {
  enabled: boolean;
  strictMode: boolean;
  detectionThresholds: {
    speedHack: {
      maxSpeed: number;
      accelerationLimit: number;
    };
    teleport: {
      maxDistance: number;
      timeWindow: number;
    };
    aimBot: {
      maxAccuracy: number;
      minReactionTime: number;
    };
    packetManipulation: {
      maxPacketLoss: number;
      maxLatencyVariance: number;
    };
  };
  actions: {
    [key in CheatType]: {
      severity: SeverityLevel;
      action: ActionType;
      threshold: number;
    };
  };
  monitoring: {
    logAllDetections: boolean;
    alertAdmins: boolean;
    saveEvidence: boolean;
    banDuration: {
      temporary: number; // 小时
      escalation: number[]; // 递增的封禁时长
    };
  };
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  reason?: string;
  correctedValue?: any;
  confidence: number;
  metadata?: Record<string, any>;
}

/**
 * 网络分析数据接口
 */
export interface NetworkAnalysis {
  playerId: string;
  connectionInfo: {
    ip: string;
    userAgent: string;
    latency: number;
    packetLoss: number;
    jitter: number;
  };
  trafficPattern: {
    packetsPerSecond: number;
    bytesPerSecond: number;
    irregularPatterns: number;
  };
  suspiciousActivity: {
    duplicatePackets: number;
    outOfOrderPackets: number;
    malformedPackets: number;
  };
}

/**
 * 管理员报告接口
 */
export interface AdminReport {
  id: string;
  reportType: 'detection' | 'summary' | 'alert';
  timestamp: Date;
  data: {
    detections: CheatDetection[];
    statistics: {
      totalDetections: number;
      detectionsByType: Record<CheatType, number>;
      actionsTaken: Record<ActionType, number>;
      falsePositives: number;
    };
    recommendations: string[];
  };
}
