/**
 * 服务发现服务
 * 提供动态服务注册、发现、健康检查、负载均衡等功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as _ from 'lodash';

/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  STARTING = 'starting',
  STOPPING = 'stopping',
  UNKNOWN = 'unknown'
}

/**
 * 服务类型枚举
 */
export enum ServiceType {
  WEB_SERVICE = 'web_service',
  API_SERVICE = 'api_service',
  DATABASE = 'database',
  MESSAGE_QUEUE = 'message_queue',
  CACHE = 'cache',
  AI_SERVICE = 'ai_service',
  IOT_SERVICE = 'iot_service',
  EDGE_SERVICE = 'edge_service'
}

/**
 * 负载均衡算法枚举
 */
export enum LoadBalancingAlgorithm {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  IP_HASH = 'ip_hash',
  LEAST_RESPONSE_TIME = 'least_response_time',
  RANDOM = 'random',
  CONSISTENT_HASH = 'consistent_hash'
}

/**
 * 服务实例接口
 */
export interface ServiceInstance {
  instanceId: string;
  serviceName: string;
  serviceType: ServiceType;
  version: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'tcp' | 'udp' | 'grpc';
  endpoints: ServiceEndpoint[];
  metadata: ServiceMetadata;
  health: HealthStatus;
  registration: RegistrationInfo;
  metrics: ServiceMetrics;
}

/**
 * 服务端点接口
 */
export interface ServiceEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  parameters?: EndpointParameter[];
  authentication?: AuthenticationInfo;
  rateLimit?: RateLimitInfo;
}

/**
 * 端点参数接口
 */
export interface EndpointParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  validation?: ValidationRule[];
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'enum';
  value: any;
  message: string;
}

/**
 * 认证信息接口
 */
export interface AuthenticationInfo {
  type: 'none' | 'basic' | 'bearer' | 'api_key' | 'oauth2';
  required: boolean;
  scopes?: string[];
}

/**
 * 限流信息接口
 */
export interface RateLimitInfo {
  requests: number;
  window: number; // seconds
  burst?: number;
}

/**
 * 服务元数据接口
 */
export interface ServiceMetadata {
  tags: string[];
  environment: 'development' | 'staging' | 'production';
  region: string;
  zone: string;
  nodeId: string;
  weight: number;
  priority: number;
  capabilities: string[];
  dependencies: ServiceDependency[];
  configuration: Record<string, any>;
}

/**
 * 服务依赖接口
 */
export interface ServiceDependency {
  serviceName: string;
  version: string;
  required: boolean;
  timeout: number;
}

/**
 * 健康状态接口
 */
export interface HealthStatus {
  status: ServiceStatus;
  lastCheck: Date;
  checkInterval: number; // seconds
  consecutiveFailures: number;
  uptime: number; // seconds
  responseTime: number; // ms
  details: HealthDetails;
}

/**
 * 健康详情接口
 */
export interface HealthDetails {
  checks: HealthCheck[];
  dependencies: DependencyHealth[];
  resources: ResourceHealth;
  custom: Record<string, any>;
}

/**
 * 健康检查接口
 */
export interface HealthCheck {
  name: string;
  status: ServiceStatus;
  message: string;
  timestamp: Date;
  duration: number; // ms
}

/**
 * 依赖健康接口
 */
export interface DependencyHealth {
  serviceName: string;
  status: ServiceStatus;
  responseTime: number;
  lastCheck: Date;
}

/**
 * 资源健康接口
 */
export interface ResourceHealth {
  cpu: number; // %
  memory: number; // %
  disk: number; // %
  network: number; // %
  connections: number;
}

/**
 * 注册信息接口
 */
export interface RegistrationInfo {
  registeredAt: Date;
  registeredBy: string;
  ttl: number; // seconds
  renewalInterval: number; // seconds
  lastRenewal: Date;
  autoRenewal: boolean;
}

/**
 * 服务指标接口
 */
export interface ServiceMetrics {
  requestCount: number;
  errorCount: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number; // requests/second
  errorRate: number; // %
  availability: number; // %
  lastUpdated: Date;
}

/**
 * 服务查询接口
 */
export interface ServiceQuery {
  serviceName?: string;
  serviceType?: ServiceType;
  tags?: string[];
  environment?: string;
  region?: string;
  zone?: string;
  status?: ServiceStatus;
  version?: string;
  minWeight?: number;
  maxResponseTime?: number;
}

/**
 * 负载均衡配置接口
 */
export interface LoadBalancerConfig {
  algorithm: LoadBalancingAlgorithm;
  healthCheck: {
    enabled: boolean;
    path: string;
    interval: number;
    timeout: number;
    retries: number;
  };
  sessionAffinity: {
    enabled: boolean;
    type: 'client_ip' | 'cookie' | 'header';
    cookieName?: string;
    headerName?: string;
    timeout: number;
  };
  circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    recoveryTimeout: number;
    halfOpenMaxCalls: number;
  };
}

@Injectable()
export class ServiceDiscoveryService {
  private readonly logger = new Logger(ServiceDiscoveryService.name);
  
  // 服务注册表
  private serviceRegistry = new Map<string, ServiceInstance>();
  private servicesByName = new Map<string, ServiceInstance[]>();
  private loadBalancerConfigs = new Map<string, LoadBalancerConfig>();
  
  // 负载均衡状态
  private roundRobinCounters = new Map<string, number>();
  private connectionCounts = new Map<string, number>();
  private responseTimes = new Map<string, number[]>();
  
  // 统计信息
  private discoveryStats = {
    totalServices: 0,
    healthyServices: 0,
    unhealthyServices: 0,
    totalRequests: 0,
    averageResponseTime: 0
  };

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeDefaultConfigs();
  }

  /**
   * 注册服务实例
   */
  public async registerService(serviceInstance: Omit<ServiceInstance, 'instanceId' | 'registration' | 'health' | 'metrics'>): Promise<string> {
    try {
      const instanceId = this.generateInstanceId(serviceInstance.serviceName, serviceInstance.host, serviceInstance.port);
      
      const fullInstance: ServiceInstance = {
        ...serviceInstance,
        instanceId,
        registration: {
          registeredAt: new Date(),
          registeredBy: 'system',
          ttl: 300, // 5 minutes
          renewalInterval: 60, // 1 minute
          lastRenewal: new Date(),
          autoRenewal: true
        },
        health: {
          status: ServiceStatus.STARTING,
          lastCheck: new Date(),
          checkInterval: 30,
          consecutiveFailures: 0,
          uptime: 0,
          responseTime: 0,
          details: {
            checks: [],
            dependencies: [],
            resources: {
              cpu: 0,
              memory: 0,
              disk: 0,
              network: 0,
              connections: 0
            },
            custom: {}
          }
        },
        metrics: {
          requestCount: 0,
          errorCount: 0,
          averageResponseTime: 0,
          p95ResponseTime: 0,
          p99ResponseTime: 0,
          throughput: 0,
          errorRate: 0,
          availability: 100,
          lastUpdated: new Date()
        }
      };
      
      // 注册到注册表
      this.serviceRegistry.set(instanceId, fullInstance);
      
      // 按服务名分组
      if (!this.servicesByName.has(serviceInstance.serviceName)) {
        this.servicesByName.set(serviceInstance.serviceName, []);
      }
      this.servicesByName.get(serviceInstance.serviceName)!.push(fullInstance);
      
      // 开始健康检查
      this.startHealthCheck(instanceId);
      
      // 发送事件
      this.eventEmitter.emit('service.registered', fullInstance);
      
      this.logger.log(`服务已注册: ${serviceInstance.serviceName} (${instanceId})`);
      return instanceId;
      
    } catch (error) {
      this.logger.error('服务注册失败:', error);
      throw error;
    }
  }

  /**
   * 注销服务实例
   */
  public async deregisterService(instanceId: string): Promise<void> {
    try {
      const instance = this.serviceRegistry.get(instanceId);
      if (!instance) {
        throw new Error(`服务实例不存在: ${instanceId}`);
      }
      
      // 从注册表移除
      this.serviceRegistry.delete(instanceId);
      
      // 从服务名分组中移除
      const serviceInstances = this.servicesByName.get(instance.serviceName);
      if (serviceInstances) {
        const index = serviceInstances.findIndex(s => s.instanceId === instanceId);
        if (index !== -1) {
          serviceInstances.splice(index, 1);
        }
        
        if (serviceInstances.length === 0) {
          this.servicesByName.delete(instance.serviceName);
        }
      }
      
      // 清理负载均衡状态
      this.cleanupLoadBalancingState(instanceId);
      
      // 发送事件
      this.eventEmitter.emit('service.deregistered', instance);
      
      this.logger.log(`服务已注销: ${instance.serviceName} (${instanceId})`);
      
    } catch (error) {
      this.logger.error('服务注销失败:', error);
      throw error;
    }
  }

  /**
   * 发现服务实例
   */
  public async discoverServices(query: ServiceQuery): Promise<ServiceInstance[]> {
    try {
      let services = Array.from(this.serviceRegistry.values());
      
      // 应用查询过滤器
      if (query.serviceName) {
        services = services.filter(s => s.serviceName === query.serviceName);
      }
      
      if (query.serviceType) {
        services = services.filter(s => s.serviceType === query.serviceType);
      }
      
      if (query.tags && query.tags.length > 0) {
        services = services.filter(s => 
          query.tags!.every(tag => s.metadata.tags.includes(tag))
        );
      }
      
      if (query.environment) {
        services = services.filter(s => s.metadata.environment === query.environment);
      }
      
      if (query.region) {
        services = services.filter(s => s.metadata.region === query.region);
      }
      
      if (query.zone) {
        services = services.filter(s => s.metadata.zone === query.zone);
      }
      
      if (query.status) {
        services = services.filter(s => s.health.status === query.status);
      }
      
      if (query.version) {
        services = services.filter(s => s.version === query.version);
      }
      
      if (query.minWeight) {
        services = services.filter(s => s.metadata.weight >= query.minWeight!);
      }
      
      if (query.maxResponseTime) {
        services = services.filter(s => s.health.responseTime <= query.maxResponseTime!);
      }
      
      // 只返回健康的服务
      services = services.filter(s => s.health.status === ServiceStatus.HEALTHY);
      
      this.logger.log(`发现服务: ${services.length} 个实例`);
      return services;
      
    } catch (error) {
      this.logger.error('服务发现失败:', error);
      throw error;
    }
  }

  /**
   * 获取负载均衡的服务实例
   */
  public async getLoadBalancedInstance(serviceName: string, clientInfo?: any): Promise<ServiceInstance | null> {
    try {
      const instances = await this.discoverServices({ serviceName });
      
      if (instances.length === 0) {
        return null;
      }
      
      const config = this.loadBalancerConfigs.get(serviceName) || this.getDefaultLoadBalancerConfig();
      
      let selectedInstance: ServiceInstance;
      
      switch (config.algorithm) {
        case LoadBalancingAlgorithm.ROUND_ROBIN:
          selectedInstance = this.roundRobinSelection(serviceName, instances);
          break;
        case LoadBalancingAlgorithm.LEAST_CONNECTIONS:
          selectedInstance = this.leastConnectionsSelection(instances);
          break;
        case LoadBalancingAlgorithm.WEIGHTED_ROUND_ROBIN:
          selectedInstance = this.weightedRoundRobinSelection(serviceName, instances);
          break;
        case LoadBalancingAlgorithm.LEAST_RESPONSE_TIME:
          selectedInstance = this.leastResponseTimeSelection(instances);
          break;
        case LoadBalancingAlgorithm.RANDOM:
          selectedInstance = this.randomSelection(instances);
          break;
        default:
          selectedInstance = this.roundRobinSelection(serviceName, instances);
      }
      
      // 更新连接计数
      this.incrementConnectionCount(selectedInstance.instanceId);
      
      this.logger.debug(`负载均衡选择: ${selectedInstance.instanceId}`);
      return selectedInstance;
      
    } catch (error) {
      this.logger.error('负载均衡失败:', error);
      throw error;
    }
  }

  /**
   * 轮询选择
   */
  private roundRobinSelection(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const selectedIndex = counter % instances.length;
    this.roundRobinCounters.set(serviceName, counter + 1);
    return instances[selectedIndex];
  }

  /**
   * 最少连接选择
   */
  private leastConnectionsSelection(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((least, current) => {
      const leastConnections = this.connectionCounts.get(least.instanceId) || 0;
      const currentConnections = this.connectionCounts.get(current.instanceId) || 0;
      return currentConnections < leastConnections ? current : least;
    });
  }

  /**
   * 加权轮询选择
   */
  private weightedRoundRobinSelection(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // 简化实现：基于权重的概率选择
    const totalWeight = instances.reduce((sum, instance) => sum + instance.metadata.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const instance of instances) {
      currentWeight += instance.metadata.weight;
      if (random <= currentWeight) {
        return instance;
      }
    }
    
    return instances[0];
  }

  /**
   * 最少响应时间选择
   */
  private leastResponseTimeSelection(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((fastest, current) => 
      current.health.responseTime < fastest.health.responseTime ? current : fastest
    );
  }

  /**
   * 随机选择
   */
  private randomSelection(instances: ServiceInstance[]): ServiceInstance {
    const randomIndex = Math.floor(Math.random() * instances.length);
    return instances[randomIndex];
  }

  /**
   * 开始健康检查
   */
  private startHealthCheck(instanceId: string): void {
    const checkHealth = async () => {
      const instance = this.serviceRegistry.get(instanceId);
      if (!instance) return;
      
      try {
        const startTime = Date.now();
        
        // 执行健康检查（简化实现）
        const isHealthy = await this.performHealthCheck(instance);
        
        const responseTime = Date.now() - startTime;
        
        // 更新健康状态
        instance.health.lastCheck = new Date();
        instance.health.responseTime = responseTime;
        
        if (isHealthy) {
          instance.health.status = ServiceStatus.HEALTHY;
          instance.health.consecutiveFailures = 0;
        } else {
          instance.health.consecutiveFailures++;
          if (instance.health.consecutiveFailures >= 3) {
            instance.health.status = ServiceStatus.UNHEALTHY;
          }
        }
        
        // 发送健康检查事件
        this.eventEmitter.emit('health.checked', {
          instanceId,
          status: instance.health.status,
          responseTime
        });
        
      } catch (error) {
        this.logger.error(`健康检查失败: ${instanceId}`, error);
        
        const instance = this.serviceRegistry.get(instanceId);
        if (instance) {
          instance.health.consecutiveFailures++;
          if (instance.health.consecutiveFailures >= 3) {
            instance.health.status = ServiceStatus.UNHEALTHY;
          }
        }
      }
    };
    
    // 立即执行一次
    checkHealth();
    
    // 定期执行
    const instance = this.serviceRegistry.get(instanceId);
    if (instance) {
      setInterval(checkHealth, instance.health.checkInterval * 1000);
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(instance: ServiceInstance): Promise<boolean> {
    // 简化实现：模拟健康检查
    // 实际应该发送HTTP请求到健康检查端点
    
    const random = Math.random();
    return random > 0.1; // 90% 健康率
  }

  /**
   * 增加连接计数
   */
  private incrementConnectionCount(instanceId: string): void {
    const current = this.connectionCounts.get(instanceId) || 0;
    this.connectionCounts.set(instanceId, current + 1);
  }

  /**
   * 减少连接计数
   */
  public decrementConnectionCount(instanceId: string): void {
    const current = this.connectionCounts.get(instanceId) || 0;
    this.connectionCounts.set(instanceId, Math.max(0, current - 1));
  }

  /**
   * 清理负载均衡状态
   */
  private cleanupLoadBalancingState(instanceId: string): void {
    this.connectionCounts.delete(instanceId);
    this.responseTimes.delete(instanceId);
  }

  /**
   * 生成实例ID
   */
  private generateInstanceId(serviceName: string, host: string, port: number): string {
    return `${serviceName}-${host}-${port}-${Date.now()}`;
  }

  /**
   * 获取默认负载均衡配置
   */
  private getDefaultLoadBalancerConfig(): LoadBalancerConfig {
    return {
      algorithm: LoadBalancingAlgorithm.ROUND_ROBIN,
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      sessionAffinity: {
        enabled: false,
        type: 'client_ip',
        timeout: 3600
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        recoveryTimeout: 60,
        halfOpenMaxCalls: 3
      }
    };
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigs(): void {
    // 可以在这里初始化一些默认的负载均衡配置
  }

  /**
   * 获取所有服务
   */
  public getAllServices(): ServiceInstance[] {
    return Array.from(this.serviceRegistry.values());
  }

  /**
   * 获取服务统计信息
   */
  public getServiceStats(): any {
    const allServices = this.getAllServices();
    
    return {
      totalServices: allServices.length,
      healthyServices: allServices.filter(s => s.health.status === ServiceStatus.HEALTHY).length,
      unhealthyServices: allServices.filter(s => s.health.status === ServiceStatus.UNHEALTHY).length,
      servicesByType: _.countBy(allServices, 'serviceType'),
      servicesByEnvironment: _.countBy(allServices, s => s.metadata.environment),
      servicesByRegion: _.countBy(allServices, s => s.metadata.region),
      averageResponseTime: _.meanBy(allServices, s => s.health.responseTime) || 0
    };
  }

  /**
   * 设置负载均衡配置
   */
  public setLoadBalancerConfig(serviceName: string, config: LoadBalancerConfig): void {
    this.loadBalancerConfigs.set(serviceName, config);
    this.logger.log(`负载均衡配置已更新: ${serviceName}`);
  }

  /**
   * 定期清理过期服务
   */
  @Cron(CronExpression.EVERY_MINUTE)
  private async cleanupExpiredServices(): Promise<void> {
    const now = new Date();
    const expiredInstances: string[] = [];
    
    for (const [instanceId, instance] of this.serviceRegistry) {
      const timeSinceRenewal = now.getTime() - instance.registration.lastRenewal.getTime();
      const ttlMs = instance.registration.ttl * 1000;
      
      if (timeSinceRenewal > ttlMs) {
        expiredInstances.push(instanceId);
      }
    }
    
    for (const instanceId of expiredInstances) {
      await this.deregisterService(instanceId);
      this.logger.warn(`清理过期服务: ${instanceId}`);
    }
  }

  /**
   * 续约服务
   */
  public async renewService(instanceId: string): Promise<void> {
    const instance = this.serviceRegistry.get(instanceId);
    if (!instance) {
      throw new Error(`服务实例不存在: ${instanceId}`);
    }
    
    instance.registration.lastRenewal = new Date();
    this.logger.debug(`服务续约: ${instanceId}`);
  }
}
