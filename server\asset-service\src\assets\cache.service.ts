/**
 * 缓存优化服务
 * 提供多级缓存、智能预加载、缓存失效等功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as crypto from 'crypto';

/**
 * 缓存级别枚举
 */
export enum CacheLevel {
  MEMORY = 'memory',
  REDIS = 'redis',
  DISK = 'disk',
  CDN = 'cdn'
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  LRU = 'lru',
  LFU = 'lfu',
  FIFO = 'fifo',
  TTL = 'ttl',
  ADAPTIVE = 'adaptive'
}

/**
 * 缓存项接口
 */
export interface CacheItem {
  key: string;
  value: any;
  size: number;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  ttl: number;
  tags: string[];
  metadata: Record<string, any>;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  level: CacheLevel;
  strategy: CacheStrategy;
  maxSize: number; // 最大缓存大小（字节）
  maxItems: number; // 最大缓存项数
  defaultTTL: number; // 默认TTL（秒）
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  persistToDisk: boolean;
  replicationEnabled: boolean;
}

/**
 * 预加载规则接口
 */
export interface PreloadRule {
  id: string;
  name: string;
  enabled: boolean;
  pattern: string; // 文件模式匹配
  conditions: PreloadCondition[];
  priority: number;
  schedule?: {
    type: 'immediate' | 'scheduled' | 'triggered';
    cron?: string;
    triggers?: string[];
  };
  target: {
    levels: CacheLevel[];
    regions?: string[];
    userGroups?: string[];
  };
}

/**
 * 预加载条件接口
 */
export interface PreloadCondition {
  type: 'time' | 'usage' | 'location' | 'user_type' | 'file_size';
  operator: 'equals' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  level: CacheLevel;
  hitRate: number; // 命中率 %
  missRate: number; // 未命中率 %
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  averageResponseTime: number; // ms
  currentSize: number; // 当前缓存大小（字节）
  currentItems: number; // 当前缓存项数
  evictionCount: number; // 淘汰次数
  errorCount: number; // 错误次数
  topKeys: Array<{
    key: string;
    hits: number;
    size: number;
    lastAccessed: Date;
  }>;
}

/**
 * 缓存操作结果接口
 */
export interface CacheOperationResult {
  success: boolean;
  level: CacheLevel;
  key: string;
  hit: boolean;
  responseTime: number;
  size?: number;
  error?: string;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  
  // 多级缓存存储
  private memoryCaches = new Map<string, CacheItem>();
  private diskCaches = new Map<string, string>(); // key -> file path
  
  // 缓存配置
  private cacheConfigs = new Map<CacheLevel, CacheConfig>();
  
  // 预加载规则
  private preloadRules = new Map<string, PreloadRule>();
  
  // 统计数据
  private stats = new Map<CacheLevel, CacheStats>();
  
  // 缓存大小限制
  private readonly maxMemorySize = 512 * 1024 * 1024; // 512MB
  private currentMemorySize = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.initializeCacheConfigs();
    this.initializeStats();
    this.startCleanupTasks();
  }

  /**
   * 获取缓存项
   */
  public async get(
    key: string,
    levels: CacheLevel[] = [CacheLevel.MEMORY, CacheLevel.REDIS, CacheLevel.DISK]
  ): Promise<CacheOperationResult> {
    const startTime = Date.now();
    
    try {
      // 按优先级检查各级缓存
      for (const level of levels) {
        const result = await this.getFromLevel(key, level);
        
        if (result.hit) {
          // 更新统计
          this.updateStats(level, true, Date.now() - startTime);
          
          // 如果从低级缓存命中，提升到高级缓存
          if (level !== CacheLevel.MEMORY && levels.includes(CacheLevel.MEMORY)) {
            await this.promoteToHigherLevel(key, result, level);
          }
          
          this.logger.debug(`缓存命中: ${key} from ${level}`);
          return result;
        }
      }
      
      // 所有级别都未命中
      levels.forEach(level => this.updateStats(level, false, Date.now() - startTime));
      
      this.logger.debug(`缓存未命中: ${key}`);
      return {
        success: false,
        level: levels[0],
        key,
        hit: false,
        responseTime: Date.now() - startTime
      };
      
    } catch (error) {
      this.logger.error(`获取缓存失败: ${key}`, error);
      return {
        success: false,
        level: levels[0],
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 设置缓存项
   */
  public async set(
    key: string,
    value: any,
    options: {
      ttl?: number;
      levels?: CacheLevel[];
      tags?: string[];
      metadata?: Record<string, any>;
      compress?: boolean;
      encrypt?: boolean;
    } = {}
  ): Promise<CacheOperationResult[]> {
    const startTime = Date.now();
    const levels = options.levels || [CacheLevel.MEMORY];
    const results: CacheOperationResult[] = [];
    
    try {
      // 创建缓存项
      const cacheItem: CacheItem = {
        key,
        value,
        size: this.calculateSize(value),
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: 0,
        ttl: options.ttl || this.cacheConfigs.get(CacheLevel.MEMORY)?.defaultTTL || 3600,
        tags: options.tags || [],
        metadata: options.metadata || {}
      };
      
      // 设置到各级缓存
      for (const level of levels) {
        const result = await this.setToLevel(key, cacheItem, level, options);
        results.push(result);
      }
      
      // 发送事件
      this.eventEmitter.emit('cache.item.set', {
        key,
        levels,
        size: cacheItem.size,
        ttl: cacheItem.ttl
      });
      
      this.logger.debug(`缓存设置: ${key} to ${levels.join(', ')}`);
      return results;
      
    } catch (error) {
      this.logger.error(`设置缓存失败: ${key}`, error);
      return levels.map(level => ({
        success: false,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        error: error.message
      }));
    }
  }

  /**
   * 删除缓存项
   */
  public async delete(
    key: string,
    levels: CacheLevel[] = [CacheLevel.MEMORY, CacheLevel.REDIS, CacheLevel.DISK]
  ): Promise<CacheOperationResult[]> {
    const startTime = Date.now();
    const results: CacheOperationResult[] = [];
    
    try {
      for (const level of levels) {
        const result = await this.deleteFromLevel(key, level);
        results.push(result);
      }
      
      // 发送事件
      this.eventEmitter.emit('cache.item.deleted', { key, levels });
      
      this.logger.debug(`缓存删除: ${key} from ${levels.join(', ')}`);
      return results;
      
    } catch (error) {
      this.logger.error(`删除缓存失败: ${key}`, error);
      return levels.map(level => ({
        success: false,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        error: error.message
      }));
    }
  }

  /**
   * 批量预加载
   */
  public async preload(
    keys: string[],
    loader: (key: string) => Promise<any>,
    options: {
      levels?: CacheLevel[];
      concurrency?: number;
      priority?: number;
    } = {}
  ): Promise<{
    success: number;
    failed: number;
    results: Array<{ key: string; success: boolean; error?: string }>;
  }> {
    try {
      this.logger.log(`开始预加载: ${keys.length} 个项目`);
      
      const concurrency = options.concurrency || 5;
      const results = [];
      
      // 分批处理
      for (let i = 0; i < keys.length; i += concurrency) {
        const batch = keys.slice(i, i + concurrency);
        const batchPromises = batch.map(async (key) => {
          try {
            const value = await loader(key);
            await this.set(key, value, { levels: options.levels });
            return { key, success: true };
          } catch (error) {
            return { key, success: false, error: error.message };
          }
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }
      
      const success = results.filter(r => r.success).length;
      const failed = results.length - success;
      
      this.logger.log(`预加载完成: ${success} 成功, ${failed} 失败`);
      
      return { success, failed, results };
      
    } catch (error) {
      this.logger.error('预加载失败:', error);
      throw error;
    }
  }

  /**
   * 按标签清除缓存
   */
  public async clearByTags(
    tags: string[],
    levels: CacheLevel[] = [CacheLevel.MEMORY, CacheLevel.REDIS, CacheLevel.DISK]
  ): Promise<{
    clearedCount: number;
    levels: CacheLevel[];
  }> {
    try {
      let clearedCount = 0;
      
      for (const level of levels) {
        const count = await this.clearLevelByTags(level, tags);
        clearedCount += count;
      }
      
      // 发送事件
      this.eventEmitter.emit('cache.cleared.by.tags', { tags, levels, clearedCount });
      
      this.logger.log(`按标签清除缓存: ${tags.join(', ')}, 清除 ${clearedCount} 项`);
      
      return { clearedCount, levels };
      
    } catch (error) {
      this.logger.error('按标签清除缓存失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(level?: CacheLevel): CacheStats | Map<CacheLevel, CacheStats> {
    if (level) {
      return this.stats.get(level) || this.createEmptyStats(level);
    }
    return new Map(this.stats);
  }

  /**
   * 优化缓存
   */
  public async optimizeCache(): Promise<{
    optimizations: Array<{
      type: 'eviction' | 'compression' | 'promotion' | 'cleanup';
      description: string;
      itemsAffected: number;
      spaceSaved: number;
    }>;
    totalSpaceSaved: number;
  }> {
    const optimizations = [];
    let totalSpaceSaved = 0;
    
    try {
      // 1. 清理过期项
      const expiredCleanup = await this.cleanupExpiredItems();
      if (expiredCleanup.itemsAffected > 0) {
        optimizations.push({
          type: 'cleanup',
          description: '清理过期缓存项',
          itemsAffected: expiredCleanup.itemsAffected,
          spaceSaved: expiredCleanup.spaceSaved
        });
        totalSpaceSaved += expiredCleanup.spaceSaved;
      }
      
      // 2. LRU淘汰
      if (this.currentMemorySize > this.maxMemorySize * 0.8) {
        const evictionResult = await this.evictLRUItems();
        if (evictionResult.itemsAffected > 0) {
          optimizations.push({
            type: 'eviction',
            description: 'LRU淘汰低频访问项',
            itemsAffected: evictionResult.itemsAffected,
            spaceSaved: evictionResult.spaceSaved
          });
          totalSpaceSaved += evictionResult.spaceSaved;
        }
      }
      
      // 3. 压缩优化
      const compressionResult = await this.compressLargeItems();
      if (compressionResult.itemsAffected > 0) {
        optimizations.push({
          type: 'compression',
          description: '压缩大型缓存项',
          itemsAffected: compressionResult.itemsAffected,
          spaceSaved: compressionResult.spaceSaved
        });
        totalSpaceSaved += compressionResult.spaceSaved;
      }
      
      this.logger.log(`缓存优化完成: 节省 ${totalSpaceSaved} 字节`);
      
      return { optimizations, totalSpaceSaved };
      
    } catch (error) {
      this.logger.error('缓存优化失败:', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化缓存配置
   */
  private initializeCacheConfigs(): void {
    this.cacheConfigs.set(CacheLevel.MEMORY, {
      level: CacheLevel.MEMORY,
      strategy: CacheStrategy.LRU,
      maxSize: this.maxMemorySize,
      maxItems: 10000,
      defaultTTL: 3600,
      compressionEnabled: false,
      encryptionEnabled: false,
      persistToDisk: false,
      replicationEnabled: false
    });
    
    this.cacheConfigs.set(CacheLevel.DISK, {
      level: CacheLevel.DISK,
      strategy: CacheStrategy.LRU,
      maxSize: 10 * 1024 * 1024 * 1024, // 10GB
      maxItems: 100000,
      defaultTTL: 86400,
      compressionEnabled: true,
      encryptionEnabled: false,
      persistToDisk: true,
      replicationEnabled: false
    });
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): void {
    for (const level of Object.values(CacheLevel)) {
      this.stats.set(level as CacheLevel, this.createEmptyStats(level as CacheLevel));
    }
  }

  /**
   * 创建空统计对象
   */
  private createEmptyStats(level: CacheLevel): CacheStats {
    return {
      level,
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      averageResponseTime: 0,
      currentSize: 0,
      currentItems: 0,
      evictionCount: 0,
      errorCount: 0,
      topKeys: []
    };
  }

  /**
   * 从指定级别获取缓存
   */
  private async getFromLevel(key: string, level: CacheLevel): Promise<CacheOperationResult> {
    const startTime = Date.now();
    
    switch (level) {
      case CacheLevel.MEMORY:
        const item = this.memoryCaches.get(key);
        if (item && !this.isExpired(item)) {
          item.lastAccessed = new Date();
          item.accessCount++;
          return {
            success: true,
            level,
            key,
            hit: true,
            responseTime: Date.now() - startTime,
            size: item.size
          };
        }
        break;
        
      case CacheLevel.DISK:
        // 简化的磁盘缓存实现
        const diskPath = this.diskCaches.get(key);
        if (diskPath) {
          return {
            success: true,
            level,
            key,
            hit: true,
            responseTime: Date.now() - startTime
          };
        }
        break;
        
      default:
        // 其他级别的实现
        break;
    }
    
    return {
      success: false,
      level,
      key,
      hit: false,
      responseTime: Date.now() - startTime
    };
  }

  /**
   * 设置到指定级别
   */
  private async setToLevel(
    key: string,
    item: CacheItem,
    level: CacheLevel,
    options: any
  ): Promise<CacheOperationResult> {
    const startTime = Date.now();
    
    try {
      switch (level) {
        case CacheLevel.MEMORY:
          // 检查内存限制
          if (this.currentMemorySize + item.size > this.maxMemorySize) {
            await this.evictLRUItems();
          }
          
          this.memoryCaches.set(key, item);
          this.currentMemorySize += item.size;
          break;
          
        case CacheLevel.DISK:
          // 简化的磁盘缓存实现
          const diskPath = `/tmp/cache/${key}`;
          this.diskCaches.set(key, diskPath);
          break;
          
        default:
          // 其他级别的实现
          break;
      }
      
      return {
        success: true,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        size: item.size
      };
      
    } catch (error) {
      return {
        success: false,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 从指定级别删除
   */
  private async deleteFromLevel(key: string, level: CacheLevel): Promise<CacheOperationResult> {
    const startTime = Date.now();
    
    try {
      switch (level) {
        case CacheLevel.MEMORY:
          const item = this.memoryCaches.get(key);
          if (item) {
            this.memoryCaches.delete(key);
            this.currentMemorySize -= item.size;
          }
          break;
          
        case CacheLevel.DISK:
          this.diskCaches.delete(key);
          break;
          
        default:
          // 其他级别的实现
          break;
      }
      
      return {
        success: true,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime
      };
      
    } catch (error) {
      return {
        success: false,
        level,
        key,
        hit: false,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查是否过期
   */
  private isExpired(item: CacheItem): boolean {
    const now = Date.now();
    const expireTime = item.createdAt.getTime() + item.ttl * 1000;
    return now > expireTime;
  }

  /**
   * 计算大小
   */
  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // 简化计算
  }

  /**
   * 更新统计
   */
  private updateStats(level: CacheLevel, hit: boolean, responseTime: number): void {
    const stats = this.stats.get(level);
    if (!stats) return;
    
    stats.totalRequests++;
    if (hit) {
      stats.totalHits++;
    } else {
      stats.totalMisses++;
    }
    
    stats.hitRate = (stats.totalHits / stats.totalRequests) * 100;
    stats.missRate = (stats.totalMisses / stats.totalRequests) * 100;
    stats.averageResponseTime = (stats.averageResponseTime + responseTime) / 2;
  }

  /**
   * 提升到高级缓存
   */
  private async promoteToHigherLevel(
    key: string,
    result: CacheOperationResult,
    fromLevel: CacheLevel
  ): Promise<void> {
    // 简化实现
    if (fromLevel === CacheLevel.DISK && result.success) {
      // 从磁盘提升到内存
      // 这里应该实现实际的提升逻辑
    }
  }

  /**
   * 按标签清除级别缓存
   */
  private async clearLevelByTags(level: CacheLevel, tags: string[]): Promise<number> {
    let count = 0;
    
    switch (level) {
      case CacheLevel.MEMORY:
        for (const [key, item] of this.memoryCaches) {
          if (item.tags.some(tag => tags.includes(tag))) {
            this.memoryCaches.delete(key);
            this.currentMemorySize -= item.size;
            count++;
          }
        }
        break;
        
      default:
        // 其他级别的实现
        break;
    }
    
    return count;
  }

  /**
   * 清理过期项
   */
  private async cleanupExpiredItems(): Promise<{ itemsAffected: number; spaceSaved: number }> {
    let itemsAffected = 0;
    let spaceSaved = 0;
    
    for (const [key, item] of this.memoryCaches) {
      if (this.isExpired(item)) {
        this.memoryCaches.delete(key);
        this.currentMemorySize -= item.size;
        itemsAffected++;
        spaceSaved += item.size;
      }
    }
    
    return { itemsAffected, spaceSaved };
  }

  /**
   * LRU淘汰
   */
  private async evictLRUItems(): Promise<{ itemsAffected: number; spaceSaved: number }> {
    const items = Array.from(this.memoryCaches.entries())
      .sort(([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime());
    
    let itemsAffected = 0;
    let spaceSaved = 0;
    const targetSize = this.maxMemorySize * 0.7; // 淘汰到70%
    
    for (const [key, item] of items) {
      if (this.currentMemorySize <= targetSize) break;
      
      this.memoryCaches.delete(key);
      this.currentMemorySize -= item.size;
      itemsAffected++;
      spaceSaved += item.size;
    }
    
    return { itemsAffected, spaceSaved };
  }

  /**
   * 压缩大型项
   */
  private async compressLargeItems(): Promise<{ itemsAffected: number; spaceSaved: number }> {
    // 简化实现，实际应该实现压缩逻辑
    return { itemsAffected: 0, spaceSaved: 0 };
  }

  /**
   * 启动清理任务
   */
  private startCleanupTasks(): void {
    // 每5分钟清理一次过期项
    setInterval(() => {
      this.cleanupExpiredItems();
    }, 5 * 60 * 1000);
    
    // 每小时优化一次缓存
    setInterval(() => {
      this.optimizeCache();
    }, 60 * 60 * 1000);
  }
}
