/**
 * CDN控制器
 * 提供CDN管理相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UploadedFile,
  UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { 
  CDNService, 
  CDNProvider, 
  UploadResult,
  DistributionStats 
} from './cdn.service';

@ApiTags('CDN管理')
@Controller('api/v1/assets/cdn')
export class CDNController {
  private readonly logger = new Logger(CDNController.name);

  constructor(private readonly cdnService: CDNService) {}

  /**
   * 上传文件到CDN
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: '上传文件到CDN' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '上传成功' })
  @ApiResponse({ status: 400, description: '上传失败' })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: {
      fileName?: string;
      contentType?: string;
      metadata?: string;
      cacheStrategy?: string;
      preferredProvider?: CDNProvider;
      redundancy?: boolean;
    }
  ): Promise<UploadResult[]> {
    try {
      if (!file) {
        throw new HttpException('文件不能为空', HttpStatus.BAD_REQUEST);
      }

      this.logger.log(`上传文件到CDN: ${file.originalname}`);

      const fileName = body.fileName || file.originalname;
      const metadata = body.metadata ? JSON.parse(body.metadata) : {};

      const results = await this.cdnService.uploadFile(
        file.buffer,
        fileName,
        {
          contentType: body.contentType || file.mimetype,
          metadata,
          cacheStrategy: body.cacheStrategy,
          preferredProvider: body.preferredProvider,
          redundancy: body.redundancy
        }
      );

      this.logger.log(`文件上传完成: ${fileName}, ${results.filter(r => r.success).length}/${results.length} 成功`);
      return results;

    } catch (error) {
      this.logger.error(`上传文件失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 生成CDN URL
   */
  @Post('url')
  @ApiOperation({ summary: '生成CDN URL' })
  @ApiResponse({ status: 200, description: '生成成功' })
  async generateCDNUrl(
    @Body() body: {
      fileName: string;
      provider?: CDNProvider;
      version?: string;
      transform?: string;
      secure?: boolean;
      expires?: string;
    }
  ): Promise<{ url: string; provider: CDNProvider }> {
    try {
      this.logger.log(`生成CDN URL: ${body.fileName}`);

      const options: any = {
        version: body.version,
        transform: body.transform,
        secure: body.secure
      };

      if (body.expires) {
        options.expires = new Date(body.expires);
      }

      const url = this.cdnService.generateCDNUrl(
        body.fileName,
        body.provider,
        options
      );

      this.logger.log(`CDN URL已生成: ${url}`);
      return { 
        url, 
        provider: body.provider || CDNProvider.AWS_CLOUDFRONT 
      };

    } catch (error) {
      this.logger.error(`生成CDN URL失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 预热缓存
   */
  @Post('warmup')
  @ApiOperation({ summary: '预热CDN缓存' })
  @ApiResponse({ status: 200, description: '预热成功' })
  async warmupCache(
    @Body() body: { urls: string[] }
  ): Promise<{
    success: number;
    failed: number;
    results: Array<{ url: string; success: boolean; error?: string }>;
  }> {
    try {
      this.logger.log(`预热缓存: ${body.urls.length} 个URL`);

      const result = await this.cdnService.warmupCache(body.urls);

      this.logger.log(`缓存预热完成: ${result.success} 成功, ${result.failed} 失败`);
      return result;

    } catch (error) {
      this.logger.error(`预热缓存失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 清除缓存
   */
  @Delete('cache')
  @ApiOperation({ summary: '清除CDN缓存' })
  @ApiResponse({ status: 200, description: '清除成功' })
  async purgeCache(
    @Body() body: {
      patterns: string[];
      provider?: CDNProvider;
    }
  ): Promise<{
    success: boolean;
    purgedCount: number;
    errors: string[];
  }> {
    try {
      this.logger.log(`清除缓存: ${body.patterns.join(', ')}`);

      const result = await this.cdnService.purgeCache(
        body.patterns,
        body.provider
      );

      this.logger.log(`缓存清除完成: ${result.purgedCount} 个文件`);
      return result;

    } catch (error) {
      this.logger.error(`清除缓存失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取分发统计
   */
  @Get('stats')
  @ApiOperation({ summary: '获取CDN分发统计' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDistributionStats(
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ): Promise<DistributionStats> {
    try {
      const timeRange = startTime && endTime ? {
        start: new Date(startTime),
        end: new Date(endTime)
      } : undefined;

      const stats = this.cdnService.getDistributionStats(timeRange);

      this.logger.log('获取CDN分发统计');
      return stats;

    } catch (error) {
      this.logger.error(`获取分发统计失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 优化CDN配置
   */
  @Post('optimize')
  @ApiOperation({ summary: '优化CDN配置' })
  @ApiResponse({ status: 200, description: '优化完成' })
  async optimizeCDNConfiguration(): Promise<{
    recommendations: Array<{
      type: 'performance' | 'cost' | 'reliability';
      description: string;
      impact: 'high' | 'medium' | 'low';
      action: string;
    }>;
    currentScore: number;
    optimizedScore: number;
  }> {
    try {
      this.logger.log('开始优化CDN配置');

      const result = await this.cdnService.optimizeCDNConfiguration();

      this.logger.log(`CDN配置优化完成: ${result.recommendations.length} 个建议`);
      return result;

    } catch (error) {
      this.logger.error(`优化CDN配置失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
