import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { GameStateService } from './game-state.service';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule
  ],
  providers: [GameStateService],
  exports: [GameStateService]
})
export class GameStateModule {}
