/**
 * 实时协作服务
 * 提供项目实时协作、同步编辑、冲突解决等功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

/**
 * 协作操作类型枚举
 */
export enum CollaborationOperationType {
  INSERT = 'insert',
  DELETE = 'delete',
  UPDATE = 'update',
  MOVE = 'move',
  CURSOR_MOVE = 'cursor_move',
  SELECTION = 'selection',
  COMMENT = 'comment',
  ANNOTATION = 'annotation'
}

/**
 * 协作操作接口
 */
export interface CollaborationOperation {
  id: string;
  projectId: string;
  userId: string;
  userName: string;
  type: CollaborationOperationType;
  target: string; // 操作目标（文件路径、节点ID等）
  position: OperationPosition;
  content: any;
  timestamp: Date;
  version: number;
  metadata: OperationMetadata;
}

/**
 * 操作位置接口
 */
export interface OperationPosition {
  line?: number;
  column?: number;
  offset?: number;
  nodeId?: string;
  path?: string;
  coordinates?: {
    x: number;
    y: number;
    z?: number;
  };
}

/**
 * 操作元数据接口
 */
export interface OperationMetadata {
  sessionId: string;
  clientId: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  platform?: string;
  conflictResolution?: ConflictResolution;
}

/**
 * 冲突解决策略接口
 */
export interface ConflictResolution {
  strategy: 'last_write_wins' | 'merge' | 'manual' | 'operational_transform';
  priority: number;
  mergeRules?: MergeRule[];
}

/**
 * 合并规则接口
 */
export interface MergeRule {
  type: 'text' | 'object' | 'array' | 'custom';
  handler: string;
  parameters: any;
}

/**
 * 协作会话接口
 */
export interface CollaborationSession {
  sessionId: string;
  projectId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  status: 'active' | 'idle' | 'disconnected';
  joinedAt: Date;
  lastActivity: Date;
  cursor: CursorPosition;
  selection: SelectionRange;
  permissions: SessionPermissions;
  clientInfo: ClientInfo;
}

/**
 * 光标位置接口
 */
export interface CursorPosition {
  target: string;
  position: OperationPosition;
  visible: boolean;
  color: string;
}

/**
 * 选择范围接口
 */
export interface SelectionRange {
  target: string;
  start: OperationPosition;
  end: OperationPosition;
  visible: boolean;
}

/**
 * 会话权限接口
 */
export interface SessionPermissions {
  canEdit: boolean;
  canComment: boolean;
  canView: boolean;
  canShare: boolean;
  canManage: boolean;
  restrictedTargets: string[];
}

/**
 * 客户端信息接口
 */
export interface ClientInfo {
  clientId: string;
  userAgent: string;
  platform: string;
  browser: string;
  version: string;
  screenResolution: {
    width: number;
    height: number;
  };
  timezone: string;
}

/**
 * 协作状态接口
 */
export interface CollaborationState {
  projectId: string;
  version: number;
  activeSessions: CollaborationSession[];
  recentOperations: CollaborationOperation[];
  conflictQueue: ConflictItem[];
  lockStatus: LockStatus;
  lastSyncTime: Date;
}

/**
 * 冲突项接口
 */
export interface ConflictItem {
  id: string;
  operations: CollaborationOperation[];
  conflictType: 'concurrent_edit' | 'version_mismatch' | 'permission_conflict';
  status: 'pending' | 'resolving' | 'resolved' | 'failed';
  createdAt: Date;
  resolvedAt?: Date;
  resolution?: any;
}

/**
 * 锁定状态接口
 */
export interface LockStatus {
  locks: ResourceLock[];
  globalLock: boolean;
  lockReason?: string;
}

/**
 * 资源锁接口
 */
export interface ResourceLock {
  resourceId: string;
  resourceType: 'file' | 'node' | 'component' | 'scene';
  lockedBy: string;
  lockedAt: Date;
  lockType: 'exclusive' | 'shared' | 'read_only';
  expiresAt?: Date;
  reason?: string;
}

@Injectable()
export class CollaborationService {
  private readonly logger = new Logger(CollaborationService.name);
  
  // 协作状态存储
  private collaborationStates = new Map<string, CollaborationState>();
  private activeSessions = new Map<string, CollaborationSession>();
  private operationHistory = new Map<string, CollaborationOperation[]>();
  
  // 配置参数
  private readonly maxOperationHistory = 1000;
  private readonly sessionTimeout = 300000; // 5分钟
  private readonly conflictResolutionTimeout = 60000; // 1分钟

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.startSessionCleanup();
  }

  /**
   * 加入协作会话
   */
  public async joinSession(
    projectId: string, 
    userId: string, 
    userName: string,
    clientInfo: ClientInfo,
    permissions: SessionPermissions
  ): Promise<CollaborationSession> {
    try {
      const sessionId = uuidv4();
      
      const session: CollaborationSession = {
        sessionId,
        projectId,
        userId,
        userName,
        status: 'active',
        joinedAt: new Date(),
        lastActivity: new Date(),
        cursor: {
          target: '',
          position: {},
          visible: false,
          color: this.generateUserColor(userId)
        },
        selection: {
          target: '',
          start: {},
          end: {},
          visible: false
        },
        permissions,
        clientInfo
      };
      
      // 存储会话
      this.activeSessions.set(sessionId, session);
      
      // 更新协作状态
      let state = this.collaborationStates.get(projectId);
      if (!state) {
        state = {
          projectId,
          version: 1,
          activeSessions: [],
          recentOperations: [],
          conflictQueue: [],
          lockStatus: {
            locks: [],
            globalLock: false
          },
          lastSyncTime: new Date()
        };
        this.collaborationStates.set(projectId, state);
      }
      
      state.activeSessions.push(session);
      state.lastSyncTime = new Date();
      
      // 发送事件
      this.eventEmitter.emit('collaboration.session.joined', {
        projectId,
        session,
        activeSessionCount: state.activeSessions.length
      });
      
      this.logger.log(`用户 ${userName} 加入项目 ${projectId} 协作会话`);
      return session;
      
    } catch (error) {
      this.logger.error('加入协作会话失败:', error);
      throw error;
    }
  }

  /**
   * 离开协作会话
   */
  public async leaveSession(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return;
      }
      
      // 移除会话
      this.activeSessions.delete(sessionId);
      
      // 更新协作状态
      const state = this.collaborationStates.get(session.projectId);
      if (state) {
        state.activeSessions = state.activeSessions.filter(s => s.sessionId !== sessionId);
        state.lastSyncTime = new Date();
        
        // 释放用户持有的锁
        state.lockStatus.locks = state.lockStatus.locks.filter(lock => lock.lockedBy !== session.userId);
      }
      
      // 发送事件
      this.eventEmitter.emit('collaboration.session.left', {
        projectId: session.projectId,
        session,
        activeSessionCount: state?.activeSessions.length || 0
      });
      
      this.logger.log(`用户 ${session.userName} 离开项目 ${session.projectId} 协作会话`);
      
    } catch (error) {
      this.logger.error('离开协作会话失败:', error);
      throw error;
    }
  }

  /**
   * 提交协作操作
   */
  public async submitOperation(
    sessionId: string, 
    operation: Omit<CollaborationOperation, 'id' | 'timestamp' | 'version'>
  ): Promise<CollaborationOperation> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('会话不存在或已过期');
      }
      
      // 检查权限
      if (!this.checkOperationPermission(session, operation)) {
        throw new Error('没有执行此操作的权限');
      }
      
      const state = this.collaborationStates.get(operation.projectId);
      if (!state) {
        throw new Error('项目协作状态不存在');
      }
      
      // 创建完整的操作对象
      const fullOperation: CollaborationOperation = {
        ...operation,
        id: uuidv4(),
        timestamp: new Date(),
        version: state.version + 1
      };
      
      // 检查冲突
      const conflicts = this.detectConflicts(fullOperation, state);
      
      if (conflicts.length > 0) {
        // 处理冲突
        await this.handleConflicts(fullOperation, conflicts, state);
      } else {
        // 直接应用操作
        await this.applyOperation(fullOperation, state);
      }
      
      // 更新会话活动时间
      session.lastActivity = new Date();
      
      // 发送事件
      this.eventEmitter.emit('collaboration.operation.submitted', {
        operation: fullOperation,
        session,
        conflicts: conflicts.length > 0
      });
      
      this.logger.debug(`操作已提交: ${fullOperation.type} by ${session.userName}`);
      return fullOperation;
      
    } catch (error) {
      this.logger.error('提交协作操作失败:', error);
      throw error;
    }
  }

  /**
   * 更新光标位置
   */
  public async updateCursor(sessionId: string, cursor: CursorPosition): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return;
      }
      
      session.cursor = cursor;
      session.lastActivity = new Date();
      
      // 广播光标位置更新
      this.eventEmitter.emit('collaboration.cursor.updated', {
        projectId: session.projectId,
        sessionId,
        userId: session.userId,
        cursor
      });
      
    } catch (error) {
      this.logger.error('更新光标位置失败:', error);
    }
  }

  /**
   * 更新选择范围
   */
  public async updateSelection(sessionId: string, selection: SelectionRange): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return;
      }
      
      session.selection = selection;
      session.lastActivity = new Date();
      
      // 广播选择范围更新
      this.eventEmitter.emit('collaboration.selection.updated', {
        projectId: session.projectId,
        sessionId,
        userId: session.userId,
        selection
      });
      
    } catch (error) {
      this.logger.error('更新选择范围失败:', error);
    }
  }

  /**
   * 获取项目协作状态
   */
  public getCollaborationState(projectId: string): CollaborationState | null {
    return this.collaborationStates.get(projectId) || null;
  }

  /**
   * 获取活跃会话
   */
  public getActiveSessions(projectId: string): CollaborationSession[] {
    const state = this.collaborationStates.get(projectId);
    return state ? state.activeSessions : [];
  }

  /**
   * 锁定资源
   */
  public async lockResource(
    sessionId: string,
    resourceId: string,
    resourceType: ResourceLock['resourceType'],
    lockType: ResourceLock['lockType'] = 'exclusive',
    reason?: string
  ): Promise<boolean> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return false;
      }
      
      const state = this.collaborationStates.get(session.projectId);
      if (!state) {
        return false;
      }
      
      // 检查是否已被锁定
      const existingLock = state.lockStatus.locks.find(lock => 
        lock.resourceId === resourceId && lock.lockType === 'exclusive'
      );
      
      if (existingLock && existingLock.lockedBy !== session.userId) {
        return false;
      }
      
      // 创建锁
      const lock: ResourceLock = {
        resourceId,
        resourceType,
        lockedBy: session.userId,
        lockedAt: new Date(),
        lockType,
        reason
      };
      
      state.lockStatus.locks.push(lock);
      
      // 发送事件
      this.eventEmitter.emit('collaboration.resource.locked', {
        projectId: session.projectId,
        lock,
        session
      });
      
      this.logger.log(`资源已锁定: ${resourceId} by ${session.userName}`);
      return true;
      
    } catch (error) {
      this.logger.error('锁定资源失败:', error);
      return false;
    }
  }

  /**
   * 解锁资源
   */
  public async unlockResource(sessionId: string, resourceId: string): Promise<boolean> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return false;
      }
      
      const state = this.collaborationStates.get(session.projectId);
      if (!state) {
        return false;
      }
      
      // 查找并移除锁
      const lockIndex = state.lockStatus.locks.findIndex(lock => 
        lock.resourceId === resourceId && lock.lockedBy === session.userId
      );
      
      if (lockIndex === -1) {
        return false;
      }
      
      const lock = state.lockStatus.locks[lockIndex];
      state.lockStatus.locks.splice(lockIndex, 1);
      
      // 发送事件
      this.eventEmitter.emit('collaboration.resource.unlocked', {
        projectId: session.projectId,
        lock,
        session
      });
      
      this.logger.log(`资源已解锁: ${resourceId} by ${session.userName}`);
      return true;
      
    } catch (error) {
      this.logger.error('解锁资源失败:', error);
      return false;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 检查操作权限
   */
  private checkOperationPermission(session: CollaborationSession, operation: any): boolean {
    if (!session.permissions.canEdit && 
        [CollaborationOperationType.INSERT, CollaborationOperationType.DELETE, CollaborationOperationType.UPDATE].includes(operation.type)) {
      return false;
    }
    
    if (!session.permissions.canComment && operation.type === CollaborationOperationType.COMMENT) {
      return false;
    }
    
    if (session.permissions.restrictedTargets.includes(operation.target)) {
      return false;
    }
    
    return true;
  }

  /**
   * 检测冲突
   */
  private detectConflicts(operation: CollaborationOperation, state: CollaborationState): ConflictItem[] {
    const conflicts: ConflictItem[] = [];
    
    // 检查并发编辑冲突
    const recentOperations = state.recentOperations.filter(op => 
      op.target === operation.target && 
      op.userId !== operation.userId &&
      Date.now() - op.timestamp.getTime() < 5000 // 5秒内的操作
    );
    
    if (recentOperations.length > 0) {
      conflicts.push({
        id: uuidv4(),
        operations: [operation, ...recentOperations],
        conflictType: 'concurrent_edit',
        status: 'pending',
        createdAt: new Date()
      });
    }
    
    return conflicts;
  }

  /**
   * 处理冲突
   */
  private async handleConflicts(
    operation: CollaborationOperation, 
    conflicts: ConflictItem[], 
    state: CollaborationState
  ): Promise<void> {
    for (const conflict of conflicts) {
      state.conflictQueue.push(conflict);
      
      // 根据冲突解决策略处理
      const strategy = operation.metadata.conflictResolution?.strategy || 'last_write_wins';
      
      switch (strategy) {
        case 'last_write_wins':
          await this.resolveByLastWriteWins(conflict, state);
          break;
        case 'operational_transform':
          await this.resolveByOperationalTransform(conflict, state);
          break;
        default:
          // 标记为需要手动解决
          conflict.status = 'pending';
      }
    }
  }

  /**
   * 应用操作
   */
  private async applyOperation(operation: CollaborationOperation, state: CollaborationState): Promise<void> {
    // 添加到历史记录
    state.recentOperations.push(operation);
    
    // 限制历史记录长度
    if (state.recentOperations.length > this.maxOperationHistory) {
      state.recentOperations = state.recentOperations.slice(-this.maxOperationHistory);
    }
    
    // 更新版本
    state.version = operation.version;
    state.lastSyncTime = new Date();
    
    // 广播操作
    this.eventEmitter.emit('collaboration.operation.applied', {
      operation,
      projectId: state.projectId
    });
  }

  /**
   * 最后写入获胜冲突解决
   */
  private async resolveByLastWriteWins(conflict: ConflictItem, state: CollaborationState): Promise<void> {
    // 选择时间戳最新的操作
    const latestOperation = conflict.operations.reduce((latest, current) => 
      current.timestamp > latest.timestamp ? current : latest
    );
    
    await this.applyOperation(latestOperation, state);
    
    conflict.status = 'resolved';
    conflict.resolvedAt = new Date();
    conflict.resolution = { strategy: 'last_write_wins', selectedOperation: latestOperation.id };
  }

  /**
   * 操作变换冲突解决
   */
  private async resolveByOperationalTransform(conflict: ConflictItem, state: CollaborationState): Promise<void> {
    // 简化的操作变换实现
    // 实际应该根据操作类型进行复杂的变换
    
    for (const operation of conflict.operations) {
      await this.applyOperation(operation, state);
    }
    
    conflict.status = 'resolved';
    conflict.resolvedAt = new Date();
    conflict.resolution = { strategy: 'operational_transform' };
  }

  /**
   * 生成用户颜色
   */
  private generateUserColor(userId: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * 启动会话清理
   */
  private startSessionCleanup(): void {
    setInterval(() => {
      const now = new Date();
      
      for (const [sessionId, session] of this.activeSessions) {
        const timeSinceActivity = now.getTime() - session.lastActivity.getTime();
        
        if (timeSinceActivity > this.sessionTimeout) {
          this.leaveSession(sessionId);
        }
      }
    }, 60000); // 每分钟检查一次
  }
}
