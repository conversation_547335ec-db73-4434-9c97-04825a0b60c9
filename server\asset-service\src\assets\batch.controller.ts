/**
 * 批量处理控制器
 * 提供批量处理相关的API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  BatchProcessingService, 
  BatchTask,
  BatchTaskType,
  BatchTaskStatus,
  BatchTaskConfig 
} from './batch-processing.service';

@ApiTags('批量处理')
@Controller('api/v1/assets/batch')
export class BatchController {
  private readonly logger = new Logger(BatchController.name);

  constructor(private readonly batchProcessingService: BatchProcessingService) {}

  /**
   * 创建批量任务
   */
  @Post('tasks')
  @ApiOperation({ summary: '创建批量处理任务' })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createBatchTask(
    @Body() body: {
      type: BatchTaskType;
      name: string;
      description?: string;
      items: Array<{ sourceUrl: string; metadata?: Record<string, any> }>;
      config: Partial<BatchTaskConfig>;
      createdBy: string;
      priority?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<BatchTask> {
    try {
      this.logger.log(`创建批量任务: ${body.name} (${body.items.length} 项)`);

      const task = await this.batchProcessingService.createBatchTask(
        body.type,
        body.name,
        body.items,
        body.config,
        body.createdBy,
        {
          description: body.description,
          priority: body.priority,
          metadata: body.metadata
        }
      );

      this.logger.log(`批量任务已创建: ${task.id}`);
      return task;

    } catch (error) {
      this.logger.error(`创建批量任务失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取任务详情
   */
  @Get('tasks/:taskId')
  @ApiOperation({ summary: '获取批量任务详情' })
  @ApiParam({ name: 'taskId', description: '任务ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async getTask(@Param('taskId') taskId: string): Promise<BatchTask> {
    try {
      const task = this.batchProcessingService.getTask(taskId);

      if (!task) {
        throw new HttpException('任务不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`获取任务详情: ${taskId}`);
      return task;

    } catch (error) {
      this.logger.error(`获取任务详情失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取任务列表
   */
  @Get('tasks')
  @ApiOperation({ summary: '获取批量任务列表' })
  @ApiQuery({ name: 'status', required: false, description: '任务状态' })
  @ApiQuery({ name: 'type', required: false, description: '任务类型' })
  @ApiQuery({ name: 'createdBy', required: false, description: '创建者' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTasks(
    @Query('status') status?: BatchTaskStatus,
    @Query('type') type?: BatchTaskType,
    @Query('createdBy') createdBy?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string
  ): Promise<{
    tasks: BatchTask[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const filters = {
        status,
        type,
        createdBy
      };

      const pagination = {
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 20
      };

      const result = this.batchProcessingService.getTasks(filters, pagination);

      this.logger.log(`获取任务列表: ${result.tasks.length}/${result.total} 个任务`);
      return result;

    } catch (error) {
      this.logger.error(`获取任务列表失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 暂停任务
   */
  @Put('tasks/:taskId/pause')
  @ApiOperation({ summary: '暂停批量任务' })
  @ApiParam({ name: 'taskId', description: '任务ID' })
  @ApiResponse({ status: 200, description: '暂停成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async pauseTask(@Param('taskId') taskId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`暂停任务: ${taskId}`);

      const success = await this.batchProcessingService.pauseTask(taskId);

      if (!success) {
        throw new HttpException('任务不存在或无法暂停', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`任务已暂停: ${taskId}`);
      return { message: '任务已暂停' };

    } catch (error) {
      this.logger.error(`暂停任务失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 恢复任务
   */
  @Put('tasks/:taskId/resume')
  @ApiOperation({ summary: '恢复批量任务' })
  @ApiParam({ name: 'taskId', description: '任务ID' })
  @ApiResponse({ status: 200, description: '恢复成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async resumeTask(@Param('taskId') taskId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`恢复任务: ${taskId}`);

      const success = await this.batchProcessingService.resumeTask(taskId);

      if (!success) {
        throw new HttpException('任务不存在或无法恢复', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`任务已恢复: ${taskId}`);
      return { message: '任务已恢复' };

    } catch (error) {
      this.logger.error(`恢复任务失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 取消任务
   */
  @Delete('tasks/:taskId')
  @ApiOperation({ summary: '取消批量任务' })
  @ApiParam({ name: 'taskId', description: '任务ID' })
  @ApiResponse({ status: 200, description: '取消成功' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async cancelTask(@Param('taskId') taskId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`取消任务: ${taskId}`);

      const success = await this.batchProcessingService.cancelTask(taskId);

      if (!success) {
        throw new HttpException('任务不存在或无法取消', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`任务已取消: ${taskId}`);
      return { message: '任务已取消' };

    } catch (error) {
      this.logger.error(`取消任务失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取任务统计
   */
  @Get('stats')
  @ApiOperation({ summary: '获取批量处理统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTaskStatistics(): Promise<{
    total: number;
    byStatus: Record<BatchTaskStatus, number>;
    byType: Record<BatchTaskType, number>;
    averageProcessingTime: number;
    successRate: number;
  }> {
    try {
      const stats = this.batchProcessingService.getTaskStatistics();

      this.logger.log('获取批量处理统计');
      return stats;

    } catch (error) {
      this.logger.error(`获取任务统计失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 批量上传任务
   */
  @Post('upload')
  @ApiOperation({ summary: '创建批量上传任务' })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  async createUploadTask(
    @Body() body: {
      name: string;
      files: Array<{ url: string; fileName?: string; metadata?: Record<string, any> }>;
      config?: {
        concurrency?: number;
        retryAttempts?: number;
        timeout?: number;
      };
      createdBy: string;
    }
  ): Promise<BatchTask> {
    try {
      this.logger.log(`创建批量上传任务: ${body.name} (${body.files.length} 文件)`);

      const items = body.files.map(file => ({
        sourceUrl: file.url,
        metadata: { fileName: file.fileName, ...file.metadata }
      }));

      const config = {
        concurrency: 3,
        retryAttempts: 3,
        timeout: 30000,
        batchSize: 10,
        pauseOnError: false,
        notifyOnCompletion: true,
        ...body.config
      };

      const task = await this.batchProcessingService.createBatchTask(
        BatchTaskType.UPLOAD,
        body.name,
        items,
        config,
        body.createdBy,
        {
          description: '批量上传文件到CDN'
        }
      );

      this.logger.log(`批量上传任务已创建: ${task.id}`);
      return task;

    } catch (error) {
      this.logger.error(`创建批量上传任务失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 批量转换任务
   */
  @Post('convert')
  @ApiOperation({ summary: '创建批量转换任务' })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  async createConvertTask(
    @Body() body: {
      name: string;
      files: Array<{ url: string; metadata?: Record<string, any> }>;
      outputFormat: string;
      quality?: number;
      config?: {
        concurrency?: number;
        retryAttempts?: number;
        timeout?: number;
      };
      createdBy: string;
    }
  ): Promise<BatchTask> {
    try {
      this.logger.log(`创建批量转换任务: ${body.name} (${body.files.length} 文件)`);

      const items = body.files.map(file => ({
        sourceUrl: file.url,
        metadata: file.metadata || {}
      }));

      const config = {
        concurrency: 2,
        retryAttempts: 3,
        timeout: 60000,
        batchSize: 5,
        pauseOnError: false,
        notifyOnCompletion: true,
        outputFormat: body.outputFormat,
        quality: body.quality || 80,
        ...body.config
      };

      const task = await this.batchProcessingService.createBatchTask(
        BatchTaskType.CONVERT,
        body.name,
        items,
        config,
        body.createdBy,
        {
          description: `批量转换文件为 ${body.outputFormat} 格式`
        }
      );

      this.logger.log(`批量转换任务已创建: ${task.id}`);
      return task;

    } catch (error) {
      this.logger.error(`创建批量转换任务失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 批量优化任务
   */
  @Post('optimize')
  @ApiOperation({ summary: '创建批量优化任务' })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  async createOptimizeTask(
    @Body() body: {
      name: string;
      files: Array<{ url: string; metadata?: Record<string, any> }>;
      quality?: number;
      config?: {
        concurrency?: number;
        retryAttempts?: number;
        timeout?: number;
      };
      createdBy: string;
    }
  ): Promise<BatchTask> {
    try {
      this.logger.log(`创建批量优化任务: ${body.name} (${body.files.length} 文件)`);

      const items = body.files.map(file => ({
        sourceUrl: file.url,
        metadata: file.metadata || {}
      }));

      const config = {
        concurrency: 3,
        retryAttempts: 3,
        timeout: 45000,
        batchSize: 8,
        pauseOnError: false,
        notifyOnCompletion: true,
        quality: body.quality || 85,
        ...body.config
      };

      const task = await this.batchProcessingService.createBatchTask(
        BatchTaskType.OPTIMIZE,
        body.name,
        items,
        config,
        body.createdBy,
        {
          description: '批量优化文件大小和质量'
        }
      );

      this.logger.log(`批量优化任务已创建: ${task.id}`);
      return task;

    } catch (error) {
      this.logger.error(`创建批量优化任务失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
