/**
 * 游戏状态类型枚举
 */
export enum GameStateType {
  PLAYER_POSITION = 'player_position',
  PLAYER_ACTION = 'player_action',
  GAME_OBJECT = 'game_object',
  ENVIRONMENT = 'environment',
  SCORE = 'score',
  INVENTORY = 'inventory',
  CUSTOM = 'custom'
}

/**
 * 同步优先级枚举
 */
export enum SyncPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 游戏状态更新接口
 */
export interface GameStateUpdate {
  id: string;
  type: GameStateType;
  playerId?: string;
  objectId?: string;
  data: any;
  timestamp: number;
  priority: SyncPriority;
  reliable: boolean; // 是否需要可靠传输
  metadata?: Record<string, any>;
}

/**
 * 游戏状态快照接口
 */
export interface GameStateSnapshot {
  roomId: string;
  timestamp: number;
  version: number;
  players: Record<string, PlayerState>;
  gameObjects: Record<string, GameObjectState>;
  environment: EnvironmentState;
  metadata: Record<string, any>;
}

/**
 * 玩家状态接口
 */
export interface PlayerState {
  id: string;
  position: Vector3;
  rotation: Vector3;
  velocity: Vector3;
  health: number;
  score: number;
  inventory: InventoryItem[];
  status: string;
  lastUpdate: number;
  metadata: Record<string, any>;
}

/**
 * 游戏对象状态接口
 */
export interface GameObjectState {
  id: string;
  type: string;
  position: Vector3;
  rotation: Vector3;
  scale: Vector3;
  properties: Record<string, any>;
  lastUpdate: number;
  ownerId?: string;
}

/**
 * 环境状态接口
 */
export interface EnvironmentState {
  weather: string;
  timeOfDay: number;
  lighting: LightingState;
  physics: PhysicsState;
  lastUpdate: number;
}

/**
 * 3D向量接口
 */
export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

/**
 * 物品接口
 */
export interface InventoryItem {
  id: string;
  type: string;
  quantity: number;
  properties: Record<string, any>;
}

/**
 * 光照状态接口
 */
export interface LightingState {
  ambientColor: string;
  directionalLight: {
    direction: Vector3;
    color: string;
    intensity: number;
  };
  shadows: boolean;
}

/**
 * 物理状态接口
 */
export interface PhysicsState {
  gravity: Vector3;
  timeScale: number;
  collisionEnabled: boolean;
}

/**
 * 状态同步配置接口
 */
export interface SyncConfig {
  maxUpdatesPerSecond: number;
  interpolationEnabled: boolean;
  predictionEnabled: boolean;
  compressionEnabled: boolean;
  deltaCompressionEnabled: boolean;
  priorityThresholds: {
    low: number;
    normal: number;
    high: number;
    critical: number;
  };
}

/**
 * 冲突解决策略枚举
 */
export enum ConflictResolutionStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  AUTHORITATIVE_SERVER = 'authoritative_server',
  PLAYER_PRIORITY = 'player_priority',
  CUSTOM = 'custom'
}

/**
 * 状态冲突接口
 */
export interface StateConflict {
  id: string;
  type: GameStateType;
  objectId: string;
  conflicts: GameStateUpdate[];
  timestamp: number;
  resolved: boolean;
  resolution?: GameStateUpdate;
}

/**
 * 同步统计接口
 */
export interface SyncStatistics {
  totalUpdates: number;
  updatesPerSecond: number;
  averageLatency: number;
  packetLoss: number;
  bandwidth: number;
  conflicts: number;
  resolvedConflicts: number;
  lastUpdate: number;
}
