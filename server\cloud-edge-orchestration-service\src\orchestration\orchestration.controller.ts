/**
 * 云边协调控制器
 * 提供资源调度、服务发现、负载均衡等API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { 
  ResourceSchedulerService, 
  SchedulingRequest, 
  SchedulingResult 
} from './resource-scheduler.service';
import { 
  ServiceDiscoveryService, 
  ServiceInstance, 
  ServiceQuery 
} from './service-discovery.service';
import { 
  LoadBalancerService, 
  LoadBalancerConfig, 
  BackendServer,
  LoadBalancingRequest 
} from './load-balancer.service';

@ApiTags('云边协调')
@Controller('api/v1/orchestration')
export class OrchestrationController {
  private readonly logger = new Logger(OrchestrationController.name);

  constructor(
    private readonly resourceScheduler: ResourceSchedulerService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly loadBalancer: LoadBalancerService
  ) {}

  // ==================== 资源调度相关API ====================

  /**
   * 提交资源调度请求
   */
  @Post('scheduling/requests')
  @ApiOperation({ summary: '提交资源调度请求' })
  @ApiResponse({ status: 201, description: '调度请求提交成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async submitSchedulingRequest(@Body() request: SchedulingRequest): Promise<SchedulingResult> {
    try {
      this.logger.log(`收到资源调度请求: ${request.requestId}`);
      
      const result = await this.resourceScheduler.submitSchedulingRequest(request);
      
      this.logger.log(`资源调度完成: ${request.requestId}, 成功: ${result.success}`);
      return result;

    } catch (error) {
      this.logger.error(`资源调度失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取调度统计信息
   */
  @Get('scheduling/stats')
  @ApiOperation({ summary: '获取资源调度统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSchedulingStats(): Promise<any> {
    try {
      const stats = this.resourceScheduler.getSchedulingStats();
      
      this.logger.log('获取资源调度统计信息');
      return stats;

    } catch (error) {
      this.logger.error(`获取调度统计信息失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取节点资源状态
   */
  @Get('scheduling/nodes')
  @ApiOperation({ summary: '获取所有节点资源状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getNodeResourceStatus(): Promise<any[]> {
    try {
      const nodes = this.resourceScheduler.getNodeResourceStatus();
      
      this.logger.log(`获取节点资源状态: ${nodes.length} 个节点`);
      return nodes;

    } catch (error) {
      this.logger.error(`获取节点资源状态失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 释放资源
   */
  @Delete('scheduling/requests/:requestId')
  @ApiOperation({ summary: '释放已分配的资源' })
  @ApiParam({ name: 'requestId', description: '调度请求ID' })
  @ApiResponse({ status: 200, description: '资源释放成功' })
  @ApiResponse({ status: 404, description: '请求不存在' })
  async releaseResources(@Param('requestId') requestId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`释放资源请求: ${requestId}`);
      
      await this.resourceScheduler.releaseResources(requestId);
      
      this.logger.log(`资源释放成功: ${requestId}`);
      return { message: '资源已释放' };

    } catch (error) {
      this.logger.error(`释放资源失败: ${error.message}`);
      if (error.message.includes('未找到')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  // ==================== 服务发现相关API ====================

  /**
   * 注册服务实例
   */
  @Post('services')
  @ApiOperation({ summary: '注册服务实例' })
  @ApiResponse({ status: 201, description: '服务注册成功' })
  @ApiResponse({ status: 400, description: '注册参数错误' })
  async registerService(@Body() serviceInstance: any): Promise<{ instanceId: string }> {
    try {
      this.logger.log(`注册服务: ${serviceInstance.serviceName}`);
      
      const instanceId = await this.serviceDiscovery.registerService(serviceInstance);
      
      this.logger.log(`服务注册成功: ${instanceId}`);
      return { instanceId };

    } catch (error) {
      this.logger.error(`服务注册失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 注销服务实例
   */
  @Delete('services/:instanceId')
  @ApiOperation({ summary: '注销服务实例' })
  @ApiParam({ name: 'instanceId', description: '服务实例ID' })
  @ApiResponse({ status: 200, description: '服务注销成功' })
  @ApiResponse({ status: 404, description: '服务实例不存在' })
  async deregisterService(@Param('instanceId') instanceId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`注销服务: ${instanceId}`);
      
      await this.serviceDiscovery.deregisterService(instanceId);
      
      this.logger.log(`服务注销成功: ${instanceId}`);
      return { message: '服务已注销' };

    } catch (error) {
      this.logger.error(`服务注销失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 发现服务实例
   */
  @Get('services')
  @ApiOperation({ summary: '发现服务实例' })
  @ApiQuery({ name: 'serviceName', required: false, description: '服务名称' })
  @ApiQuery({ name: 'serviceType', required: false, description: '服务类型' })
  @ApiQuery({ name: 'environment', required: false, description: '环境' })
  @ApiQuery({ name: 'region', required: false, description: '区域' })
  @ApiResponse({ status: 200, description: '发现成功' })
  async discoverServices(
    @Query('serviceName') serviceName?: string,
    @Query('serviceType') serviceType?: string,
    @Query('environment') environment?: string,
    @Query('region') region?: string
  ): Promise<ServiceInstance[]> {
    try {
      const query: ServiceQuery = {
        serviceName,
        serviceType: serviceType as any,
        environment: environment as any,
        region
      };
      
      const services = await this.serviceDiscovery.discoverServices(query);
      
      this.logger.log(`发现服务: ${services.length} 个实例`);
      return services;

    } catch (error) {
      this.logger.error(`服务发现失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取负载均衡的服务实例
   */
  @Post('services/:serviceName/load-balanced')
  @ApiOperation({ summary: '获取负载均衡的服务实例' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '没有可用的服务实例' })
  async getLoadBalancedInstance(
    @Param('serviceName') serviceName: string,
    @Body() clientInfo?: any
  ): Promise<ServiceInstance> {
    try {
      this.logger.log(`获取负载均衡实例: ${serviceName}`);
      
      const instance = await this.serviceDiscovery.getLoadBalancedInstance(serviceName, clientInfo);
      
      if (!instance) {
        throw new HttpException('没有可用的服务实例', HttpStatus.NOT_FOUND);
      }
      
      this.logger.log(`负载均衡实例: ${instance.instanceId}`);
      return instance;

    } catch (error) {
      this.logger.error(`获取负载均衡实例失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 续约服务
   */
  @Put('services/:instanceId/renew')
  @ApiOperation({ summary: '续约服务实例' })
  @ApiParam({ name: 'instanceId', description: '服务实例ID' })
  @ApiResponse({ status: 200, description: '续约成功' })
  @ApiResponse({ status: 404, description: '服务实例不存在' })
  async renewService(@Param('instanceId') instanceId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`续约服务: ${instanceId}`);
      
      await this.serviceDiscovery.renewService(instanceId);
      
      this.logger.log(`服务续约成功: ${instanceId}`);
      return { message: '服务已续约' };

    } catch (error) {
      this.logger.error(`服务续约失败: ${error.message}`);
      if (error.message.includes('不存在')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取服务统计信息
   */
  @Get('services/stats')
  @ApiOperation({ summary: '获取服务发现统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getServiceStats(): Promise<any> {
    try {
      const stats = this.serviceDiscovery.getServiceStats();
      
      this.logger.log('获取服务发现统计信息');
      return stats;

    } catch (error) {
      this.logger.error(`获取服务统计信息失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // ==================== 负载均衡相关API ====================

  /**
   * 创建负载均衡器
   */
  @Post('load-balancers')
  @ApiOperation({ summary: '创建负载均衡器' })
  @ApiResponse({ status: 201, description: '负载均衡器创建成功' })
  @ApiResponse({ status: 400, description: '配置参数错误' })
  async createLoadBalancer(@Body() config: LoadBalancerConfig): Promise<{ message: string }> {
    try {
      this.logger.log(`创建负载均衡器: ${config.name}`);
      
      this.loadBalancer.createLoadBalancer(config);
      
      this.logger.log(`负载均衡器创建成功: ${config.name}`);
      return { message: '负载均衡器已创建' };

    } catch (error) {
      this.logger.error(`创建负载均衡器失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取负载均衡器配置
   */
  @Get('load-balancers/:name')
  @ApiOperation({ summary: '获取负载均衡器配置' })
  @ApiParam({ name: 'name', description: '负载均衡器名称' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '负载均衡器不存在' })
  async getLoadBalancer(@Param('name') name: string): Promise<LoadBalancerConfig> {
    try {
      const config = this.loadBalancer.getLoadBalancer(name);
      
      if (!config) {
        throw new HttpException('负载均衡器不存在', HttpStatus.NOT_FOUND);
      }
      
      this.logger.log(`获取负载均衡器配置: ${name}`);
      return config;

    } catch (error) {
      this.logger.error(`获取负载均衡器配置失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 选择后端服务器
   */
  @Post('load-balancers/:name/select')
  @ApiOperation({ summary: '选择后端服务器' })
  @ApiParam({ name: 'name', description: '负载均衡器名称' })
  @ApiResponse({ status: 200, description: '选择成功' })
  @ApiResponse({ status: 404, description: '负载均衡器不存在或没有可用服务器' })
  async selectServer(
    @Param('name') name: string,
    @Body() request: LoadBalancingRequest
  ): Promise<any> {
    try {
      this.logger.log(`选择后端服务器: ${name}`);
      
      const response = await this.loadBalancer.selectServer(name, request);
      
      this.logger.log(`服务器选择成功: ${response.selectedServer.serverId}`);
      return response;

    } catch (error) {
      this.logger.error(`选择后端服务器失败: ${error.message}`);
      if (error.message.includes('不存在') || error.message.includes('没有可用')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 获取负载均衡统计信息
   */
  @Get('load-balancers/stats')
  @ApiOperation({ summary: '获取负载均衡统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLoadBalancingStats(): Promise<any> {
    try {
      const stats = this.loadBalancer.getLoadBalancingStats();
      
      this.logger.log('获取负载均衡统计信息');
      return stats;

    } catch (error) {
      this.logger.error(`获取负载均衡统计信息失败: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
