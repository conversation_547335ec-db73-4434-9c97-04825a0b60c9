/**
 * 权限管理服务
 * 提供细粒度的项目权限控制、角色管理、访问控制等功能
 */

import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ProjectMember, ProjectMemberRole } from './entities/project-member.entity';

/**
 * 权限枚举
 */
export enum Permission {
  // 项目级权限
  PROJECT_VIEW = 'project:view',
  PROJECT_EDIT = 'project:edit',
  PROJECT_DELETE = 'project:delete',
  PROJECT_MANAGE = 'project:manage',
  PROJECT_SHARE = 'project:share',
  
  // 成员管理权限
  MEMBER_VIEW = 'member:view',
  MEMBER_INVITE = 'member:invite',
  MEMBER_REMOVE = 'member:remove',
  MEMBER_MANAGE_ROLES = 'member:manage_roles',
  
  // 内容权限
  CONTENT_VIEW = 'content:view',
  CONTENT_EDIT = 'content:edit',
  CONTENT_CREATE = 'content:create',
  CONTENT_DELETE = 'content:delete',
  CONTENT_PUBLISH = 'content:publish',
  
  // 版本控制权限
  VERSION_VIEW = 'version:view',
  VERSION_CREATE = 'version:create',
  VERSION_MERGE = 'version:merge',
  VERSION_ROLLBACK = 'version:rollback',
  VERSION_TAG = 'version:tag',
  
  // 协作权限
  COLLABORATE_VIEW = 'collaborate:view',
  COLLABORATE_EDIT = 'collaborate:edit',
  COLLABORATE_COMMENT = 'collaborate:comment',
  COLLABORATE_REVIEW = 'collaborate:review',
  
  // 设置权限
  SETTINGS_VIEW = 'settings:view',
  SETTINGS_EDIT = 'settings:edit',
  SETTINGS_ADVANCED = 'settings:advanced',
  
  // 部署权限
  DEPLOY_VIEW = 'deploy:view',
  DEPLOY_EXECUTE = 'deploy:execute',
  DEPLOY_MANAGE = 'deploy:manage',
  
  // 分析权限
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export'
}

/**
 * 资源类型枚举
 */
export enum ResourceType {
  PROJECT = 'project',
  FILE = 'file',
  FOLDER = 'folder',
  COMPONENT = 'component',
  SCENE = 'scene',
  ASSET = 'asset',
  BRANCH = 'branch',
  VERSION = 'version',
  DEPLOYMENT = 'deployment'
}

/**
 * 权限规则接口
 */
export interface PermissionRule {
  id: string;
  resourceType: ResourceType;
  resourceId: string;
  permission: Permission;
  effect: 'allow' | 'deny';
  conditions?: PermissionCondition[];
  priority: number;
  createdAt: Date;
  expiresAt?: Date;
}

/**
 * 权限条件接口
 */
export interface PermissionCondition {
  type: 'time' | 'location' | 'device' | 'custom';
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
  metadata?: any;
}

/**
 * 角色定义接口
 */
export interface RoleDefinition {
  role: ProjectMemberRole;
  permissions: Permission[];
  description: string;
  isCustom: boolean;
  inheritFrom?: ProjectMemberRole;
  restrictions?: RoleRestriction[];
}

/**
 * 角色限制接口
 */
export interface RoleRestriction {
  type: 'resource_limit' | 'time_limit' | 'action_limit';
  parameters: any;
  description: string;
}

/**
 * 权限上下文接口
 */
export interface PermissionContext {
  userId: string;
  projectId: string;
  resourceType: ResourceType;
  resourceId: string;
  action: Permission;
  metadata?: {
    timestamp?: Date;
    clientIp?: string;
    userAgent?: string;
    sessionId?: string;
    [key: string]: any;
  };
}

/**
 * 权限检查结果接口
 */
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  appliedRules: PermissionRule[];
  suggestions?: string[];
  requiredRole?: ProjectMemberRole;
}

/**
 * 权限审计日志接口
 */
export interface PermissionAuditLog {
  id: string;
  userId: string;
  userName: string;
  projectId: string;
  action: Permission;
  resourceType: ResourceType;
  resourceId: string;
  result: 'allowed' | 'denied';
  reason?: string;
  context: PermissionContext;
  timestamp: Date;
  sessionId?: string;
}

@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);
  
  // 权限规则存储
  private permissionRules = new Map<string, PermissionRule[]>();
  private auditLogs: PermissionAuditLog[] = [];
  
  // 角色定义
  private roleDefinitions = new Map<ProjectMemberRole, RoleDefinition>();

  constructor(
    @InjectRepository(ProjectMember)
    private readonly projectMemberRepository: Repository<ProjectMember>,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.initializeDefaultRoles();
  }

  /**
   * 检查权限
   */
  public async checkPermission(context: PermissionContext): Promise<PermissionCheckResult> {
    try {
      // 获取用户在项目中的角色
      const member = await this.projectMemberRepository.findOne({
        where: {
          projectId: context.projectId,
          userId: context.userId
        }
      });

      if (!member) {
        const result = {
          allowed: false,
          reason: '用户不是项目成员',
          appliedRules: [],
          requiredRole: ProjectMemberRole.VIEWER
        };
        
        await this.logPermissionCheck(context, result);
        return result;
      }

      // 检查角色权限
      const rolePermissions = this.getRolePermissions(member.role);
      const hasRolePermission = rolePermissions.includes(context.action);

      // 获取资源特定的权限规则
      const resourceRules = this.getResourcePermissionRules(
        context.resourceType,
        context.resourceId
      );

      // 应用权限规则
      const ruleResult = this.applyPermissionRules(
        context,
        resourceRules,
        hasRolePermission
      );

      const result: PermissionCheckResult = {
        allowed: ruleResult.allowed,
        reason: ruleResult.reason,
        appliedRules: ruleResult.appliedRules,
        suggestions: this.generateSuggestions(context, member.role)
      };

      // 记录审计日志
      await this.logPermissionCheck(context, result);

      return result;

    } catch (error) {
      this.logger.error('权限检查失败:', error);
      
      const result = {
        allowed: false,
        reason: '权限检查系统错误',
        appliedRules: []
      };
      
      await this.logPermissionCheck(context, result);
      return result;
    }
  }

  /**
   * 批量检查权限
   */
  public async checkPermissions(
    contexts: PermissionContext[]
  ): Promise<PermissionCheckResult[]> {
    const results = await Promise.all(
      contexts.map(context => this.checkPermission(context))
    );
    
    return results;
  }

  /**
   * 验证权限（抛出异常）
   */
  public async requirePermission(context: PermissionContext): Promise<void> {
    const result = await this.checkPermission(context);
    
    if (!result.allowed) {
      throw new ForbiddenException(
        result.reason || '没有执行此操作的权限'
      );
    }
  }

  /**
   * 获取用户权限列表
   */
  public async getUserPermissions(
    userId: string, 
    projectId: string
  ): Promise<Permission[]> {
    const member = await this.projectMemberRepository.findOne({
      where: { projectId, userId }
    });

    if (!member) {
      return [];
    }

    const rolePermissions = this.getRolePermissions(member.role);
    
    // 获取额外的权限规则
    const additionalPermissions = this.getAdditionalPermissions(
      userId,
      projectId
    );

    return [...new Set([...rolePermissions, ...additionalPermissions])];
  }

  /**
   * 添加权限规则
   */
  public addPermissionRule(
    resourceType: ResourceType,
    resourceId: string,
    rule: Omit<PermissionRule, 'id' | 'createdAt'>
  ): string {
    const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullRule: PermissionRule = {
      ...rule,
      id: ruleId,
      createdAt: new Date()
    };

    const key = `${resourceType}:${resourceId}`;
    const rules = this.permissionRules.get(key) || [];
    rules.push(fullRule);
    
    // 按优先级排序
    rules.sort((a, b) => b.priority - a.priority);
    
    this.permissionRules.set(key, rules);

    this.eventEmitter.emit('permission.rule.added', {
      rule: fullRule,
      resourceType,
      resourceId
    });

    this.logger.log(`权限规则已添加: ${ruleId} for ${resourceType}:${resourceId}`);
    return ruleId;
  }

  /**
   * 移除权限规则
   */
  public removePermissionRule(
    resourceType: ResourceType,
    resourceId: string,
    ruleId: string
  ): boolean {
    const key = `${resourceType}:${resourceId}`;
    const rules = this.permissionRules.get(key) || [];
    
    const index = rules.findIndex(rule => rule.id === ruleId);
    if (index === -1) {
      return false;
    }

    const removedRule = rules.splice(index, 1)[0];
    this.permissionRules.set(key, rules);

    this.eventEmitter.emit('permission.rule.removed', {
      rule: removedRule,
      resourceType,
      resourceId
    });

    this.logger.log(`权限规则已移除: ${ruleId}`);
    return true;
  }

  /**
   * 获取权限审计日志
   */
  public getAuditLogs(
    projectId: string,
    filters?: {
      userId?: string;
      action?: Permission;
      resourceType?: ResourceType;
      result?: 'allowed' | 'denied';
      startTime?: Date;
      endTime?: Date;
    },
    limit: number = 100
  ): PermissionAuditLog[] {
    let logs = this.auditLogs.filter(log => log.projectId === projectId);

    if (filters) {
      if (filters.userId) {
        logs = logs.filter(log => log.userId === filters.userId);
      }
      if (filters.action) {
        logs = logs.filter(log => log.action === filters.action);
      }
      if (filters.resourceType) {
        logs = logs.filter(log => log.resourceType === filters.resourceType);
      }
      if (filters.result) {
        logs = logs.filter(log => log.result === filters.result);
      }
      if (filters.startTime) {
        logs = logs.filter(log => log.timestamp >= filters.startTime!);
      }
      if (filters.endTime) {
        logs = logs.filter(log => log.timestamp <= filters.endTime!);
      }
    }

    return logs
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 获取角色定义
   */
  public getRoleDefinition(role: ProjectMemberRole): RoleDefinition | null {
    return this.roleDefinitions.get(role) || null;
  }

  /**
   * 获取所有角色定义
   */
  public getAllRoleDefinitions(): RoleDefinition[] {
    return Array.from(this.roleDefinitions.values());
  }

  // ==================== 私有方法 ====================

  /**
   * 获取角色权限
   */
  private getRolePermissions(role: ProjectMemberRole): Permission[] {
    const roleDefinition = this.roleDefinitions.get(role);
    if (!roleDefinition) {
      return [];
    }

    let permissions = [...roleDefinition.permissions];

    // 继承父角色权限
    if (roleDefinition.inheritFrom) {
      const parentPermissions = this.getRolePermissions(roleDefinition.inheritFrom);
      permissions = [...new Set([...permissions, ...parentPermissions])];
    }

    return permissions;
  }

  /**
   * 获取资源权限规则
   */
  private getResourcePermissionRules(
    resourceType: ResourceType,
    resourceId: string
  ): PermissionRule[] {
    const key = `${resourceType}:${resourceId}`;
    const rules = this.permissionRules.get(key) || [];
    
    // 过滤过期规则
    const now = new Date();
    return rules.filter(rule => !rule.expiresAt || rule.expiresAt > now);
  }

  /**
   * 应用权限规则
   */
  private applyPermissionRules(
    context: PermissionContext,
    rules: PermissionRule[],
    hasRolePermission: boolean
  ): {
    allowed: boolean;
    reason?: string;
    appliedRules: PermissionRule[];
  } {
    const appliedRules: PermissionRule[] = [];
    let finalDecision = hasRolePermission;
    let reason = hasRolePermission ? '角色权限允许' : '角色权限不足';

    // 按优先级应用规则
    for (const rule of rules) {
      if (rule.permission !== context.action) {
        continue;
      }

      // 检查条件
      if (rule.conditions && !this.evaluateConditions(rule.conditions, context)) {
        continue;
      }

      appliedRules.push(rule);

      if (rule.effect === 'deny') {
        finalDecision = false;
        reason = '被权限规则拒绝';
        break; // 拒绝规则优先级最高
      } else if (rule.effect === 'allow') {
        finalDecision = true;
        reason = '被权限规则允许';
      }
    }

    return {
      allowed: finalDecision,
      reason,
      appliedRules
    };
  }

  /**
   * 评估权限条件
   */
  private evaluateConditions(
    conditions: PermissionCondition[],
    context: PermissionContext
  ): boolean {
    return conditions.every(condition => {
      switch (condition.type) {
        case 'time':
          return this.evaluateTimeCondition(condition, context);
        case 'location':
          return this.evaluateLocationCondition(condition, context);
        case 'device':
          return this.evaluateDeviceCondition(condition, context);
        default:
          return true;
      }
    });
  }

  /**
   * 评估时间条件
   */
  private evaluateTimeCondition(
    condition: PermissionCondition,
    context: PermissionContext
  ): boolean {
    const now = context.metadata?.timestamp || new Date();
    const value = new Date(condition.value);

    switch (condition.operator) {
      case 'greater_than':
        return now > value;
      case 'less_than':
        return now < value;
      default:
        return true;
    }
  }

  /**
   * 评估位置条件
   */
  private evaluateLocationCondition(
    condition: PermissionCondition,
    context: PermissionContext
  ): boolean {
    // 简化实现，实际应该根据IP地址或GPS坐标判断
    return true;
  }

  /**
   * 评估设备条件
   */
  private evaluateDeviceCondition(
    condition: PermissionCondition,
    context: PermissionContext
  ): boolean {
    const userAgent = context.metadata?.userAgent || '';
    
    switch (condition.operator) {
      case 'in':
        return condition.value.some((device: string) => 
          userAgent.toLowerCase().includes(device.toLowerCase())
        );
      default:
        return true;
    }
  }

  /**
   * 获取额外权限
   */
  private getAdditionalPermissions(
    userId: string,
    projectId: string
  ): Permission[] {
    // 这里可以实现基于用户特定规则的额外权限
    return [];
  }

  /**
   * 生成建议
   */
  private generateSuggestions(
    context: PermissionContext,
    currentRole: ProjectMemberRole
  ): string[] {
    const suggestions: string[] = [];

    // 根据当前角色和所需权限生成建议
    const requiredPermissions = [context.action];
    const currentPermissions = this.getRolePermissions(currentRole);

    if (!currentPermissions.includes(context.action)) {
      // 找到包含所需权限的最低角色
      for (const [role, definition] of this.roleDefinitions) {
        if (definition.permissions.includes(context.action)) {
          suggestions.push(`需要 ${role} 角色或更高权限`);
          break;
        }
      }
    }

    return suggestions;
  }

  /**
   * 记录权限检查日志
   */
  private async logPermissionCheck(
    context: PermissionContext,
    result: PermissionCheckResult
  ): Promise<void> {
    const log: PermissionAuditLog = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: context.userId,
      userName: 'Unknown', // 实际应该从用户服务获取
      projectId: context.projectId,
      action: context.action,
      resourceType: context.resourceType,
      resourceId: context.resourceId,
      result: result.allowed ? 'allowed' : 'denied',
      reason: result.reason,
      context,
      timestamp: new Date(),
      sessionId: context.metadata?.sessionId
    };

    this.auditLogs.push(log);

    // 限制日志数量
    if (this.auditLogs.length > 10000) {
      this.auditLogs = this.auditLogs.slice(-5000);
    }

    // 发送事件
    this.eventEmitter.emit('permission.checked', log);
  }

  /**
   * 初始化默认角色
   */
  private initializeDefaultRoles(): void {
    // 所有者角色
    this.roleDefinitions.set(ProjectMemberRole.OWNER, {
      role: ProjectMemberRole.OWNER,
      permissions: Object.values(Permission),
      description: '项目所有者，拥有所有权限',
      isCustom: false
    });

    // 管理员角色
    this.roleDefinitions.set(ProjectMemberRole.ADMIN, {
      role: ProjectMemberRole.ADMIN,
      permissions: [
        Permission.PROJECT_VIEW,
        Permission.PROJECT_EDIT,
        Permission.PROJECT_SHARE,
        Permission.MEMBER_VIEW,
        Permission.MEMBER_INVITE,
        Permission.MEMBER_REMOVE,
        Permission.CONTENT_VIEW,
        Permission.CONTENT_EDIT,
        Permission.CONTENT_CREATE,
        Permission.CONTENT_DELETE,
        Permission.CONTENT_PUBLISH,
        Permission.VERSION_VIEW,
        Permission.VERSION_CREATE,
        Permission.VERSION_MERGE,
        Permission.VERSION_TAG,
        Permission.COLLABORATE_VIEW,
        Permission.COLLABORATE_EDIT,
        Permission.COLLABORATE_COMMENT,
        Permission.COLLABORATE_REVIEW,
        Permission.SETTINGS_VIEW,
        Permission.SETTINGS_EDIT,
        Permission.DEPLOY_VIEW,
        Permission.DEPLOY_EXECUTE,
        Permission.ANALYTICS_VIEW
      ],
      description: '项目管理员，拥有大部分管理权限',
      isCustom: false
    });

    // 编辑者角色
    this.roleDefinitions.set(ProjectMemberRole.EDITOR, {
      role: ProjectMemberRole.EDITOR,
      permissions: [
        Permission.PROJECT_VIEW,
        Permission.CONTENT_VIEW,
        Permission.CONTENT_EDIT,
        Permission.CONTENT_CREATE,
        Permission.VERSION_VIEW,
        Permission.VERSION_CREATE,
        Permission.COLLABORATE_VIEW,
        Permission.COLLABORATE_EDIT,
        Permission.COLLABORATE_COMMENT,
        Permission.SETTINGS_VIEW
      ],
      description: '内容编辑者，可以编辑项目内容',
      isCustom: false
    });

    // 查看者角色
    this.roleDefinitions.set(ProjectMemberRole.VIEWER, {
      role: ProjectMemberRole.VIEWER,
      permissions: [
        Permission.PROJECT_VIEW,
        Permission.CONTENT_VIEW,
        Permission.VERSION_VIEW,
        Permission.COLLABORATE_VIEW,
        Permission.COLLABORATE_COMMENT
      ],
      description: '只读访问者，只能查看项目内容',
      isCustom: false
    });
  }
}
