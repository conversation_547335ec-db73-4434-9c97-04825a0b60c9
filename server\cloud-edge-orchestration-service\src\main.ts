/**
 * 云边协同编排服务主入口文件
 * DL引擎 - 混合云部署、边缘计算网络、5G网络应用
 */

import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('CloudEdgeOrchestrationService');
  
  // 创建NestJS应用实例
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  // 启用CORS
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // Swagger API文档配置
  const config = new DocumentBuilder()
    .setTitle('云边协同编排服务 API')
    .setDescription('DL引擎云边协同编排服务 - 混合云部署、边缘计算网络、5G网络应用管理、智能资源调度、服务发现、负载均衡')
    .setVersion('1.0.0')
    .addTag('cloud-edge', '云边协同编排')
    .addTag('edge-nodes', '边缘节点管理')
    .addTag('workloads', '工作负载管理')
    .addTag('network-slicing', '网络切片管理')
    .addTag('resource-optimization', '资源优化')
    .addTag('云边协调', '智能资源调度、服务发现、负载均衡')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 启动服务
  const port = process.env.PORT || 3003;
  await app.listen(port);
  
  logger.log(`🚀 云边协同编排服务已启动`);
  logger.log(`📡 服务地址: http://localhost:${port}`);
  logger.log(`📚 API文档: http://localhost:${port}/api/docs`);
  logger.log(`🌐 服务前缀: /api/v1`);
}

// 启动应用
bootstrap().catch((error) => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
