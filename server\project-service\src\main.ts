/**
 * 项目服务入口文件
 */
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('PROJECT_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('PROJECT_SERVICE_PORT', 3002),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局过滤器和拦截器
  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalInterceptors(new LoggingInterceptor());
  app.useGlobalInterceptors(new TransformInterceptor());

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS
  app.enableCors();
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet());
  
  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('项目服务API')
    .setDescription('DL（Digital Learning）引擎项目服务API文档 - 项目管理、实时协作、版本控制、权限管理')
    .setVersion('1.0')
    .addTag('项目管理', '项目的创建、编辑、删除等基础管理功能')
    .addTag('项目协作', '实时协作、同步编辑、冲突解决等协作功能')
    .addTag('版本控制', '版本管理、分支管理、合并等版本控制功能')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const httpPort = configService.get<number>('PROJECT_SERVICE_HTTP_PORT', 4002);
  await app.listen(httpPort);

  logger.log(`项目服务已启动`);
  logger.log(`微服务端口: ${configService.get<number>('PROJECT_SERVICE_PORT', 3002)}`);
  logger.log(`HTTP端口: ${httpPort}`);
  logger.log(`Swagger文档: http://localhost:${httpPort}/api/docs`);
}

bootstrap();
