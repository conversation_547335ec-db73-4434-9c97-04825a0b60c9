/**
 * 云边协同编排服务应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CloudEdgeModule } from './modules/cloud-edge/cloud-edge.module';
import { EdgeNodeModule } from './modules/edge-node/edge-node.module';
import { WorkloadModule } from './modules/workload/workload.module';
import { NetworkSlicingModule } from './modules/network-slicing/network-slicing.module';
import { ResourceOptimizationModule } from './modules/resource-optimization/resource-optimization.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';
import { HealthModule } from './modules/health/health.module';
import { OrchestrationModule } from './orchestration/orchestration.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 调度模块
    ScheduleModule.forRoot(),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 数据库模块 (可选，如果需要持久化)
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      username: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'cloud_edge_orchestration',
      entities: [],
      synchronize: process.env.NODE_ENV !== 'production',
      logging: process.env.NODE_ENV === 'development',
    }),

    // 业务模块
    CloudEdgeModule,
    EdgeNodeModule,
    WorkloadModule,
    NetworkSlicingModule,
    ResourceOptimizationModule,
    MonitoringModule,
    HealthModule,
    OrchestrationModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
