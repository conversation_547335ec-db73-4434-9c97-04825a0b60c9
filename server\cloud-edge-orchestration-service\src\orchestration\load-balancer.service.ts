/**
 * 智能负载均衡服务
 * 提供多种负载均衡算法、流量控制、故障转移等功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as _ from 'lodash';

/**
 * 负载均衡算法枚举
 */
export enum LoadBalancingAlgorithm {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  WEIGHTED_LEAST_CONNECTIONS = 'weighted_least_connections',
  IP_HASH = 'ip_hash',
  CONSISTENT_HASH = 'consistent_hash',
  LEAST_RESPONSE_TIME = 'least_response_time',
  RANDOM = 'random',
  WEIGHTED_RANDOM = 'weighted_random',
  RESOURCE_BASED = 'resource_based',
  GEOGRAPHIC = 'geographic',
  AI_OPTIMIZED = 'ai_optimized'
}

/**
 * 流量控制策略枚举
 */
export enum TrafficControlStrategy {
  RATE_LIMITING = 'rate_limiting',
  CIRCUIT_BREAKER = 'circuit_breaker',
  BULKHEAD = 'bulkhead',
  TIMEOUT = 'timeout',
  RETRY = 'retry',
  THROTTLING = 'throttling'
}

/**
 * 后端服务器接口
 */
export interface BackendServer {
  serverId: string;
  name: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'tcp' | 'udp';
  weight: number;
  priority: number;
  zone: string;
  region: string;
  status: ServerStatus;
  health: ServerHealth;
  metrics: ServerMetrics;
  configuration: ServerConfiguration;
}

/**
 * 服务器状态枚举
 */
export enum ServerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAINING = 'draining',
  MAINTENANCE = 'maintenance',
  FAILED = 'failed'
}

/**
 * 服务器健康状态接口
 */
export interface ServerHealth {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
  responseTime: number; // ms
  uptime: number; // seconds
  healthScore: number; // 0-100
}

/**
 * 服务器指标接口
 */
export interface ServerMetrics {
  activeConnections: number;
  totalRequests: number;
  requestsPerSecond: number;
  errorRate: number; // %
  averageResponseTime: number; // ms
  p95ResponseTime: number; // ms
  p99ResponseTime: number; // ms
  cpuUsage: number; // %
  memoryUsage: number; // %
  networkUsage: number; // %
  lastUpdated: Date;
}

/**
 * 服务器配置接口
 */
export interface ServerConfiguration {
  maxConnections: number;
  connectionTimeout: number; // ms
  readTimeout: number; // ms
  writeTimeout: number; // ms
  keepAliveTimeout: number; // ms
  retryAttempts: number;
  retryDelay: number; // ms
  circuitBreakerThreshold: number;
  rateLimit: RateLimitConfig;
}

/**
 * 限流配置接口
 */
export interface RateLimitConfig {
  enabled: boolean;
  requestsPerSecond: number;
  burstSize: number;
  windowSize: number; // seconds
  strategy: 'token_bucket' | 'sliding_window' | 'fixed_window';
}

/**
 * 负载均衡器配置接口
 */
export interface LoadBalancerConfig {
  name: string;
  algorithm: LoadBalancingAlgorithm;
  servers: BackendServer[];
  healthCheck: HealthCheckConfig;
  sessionAffinity: SessionAffinityConfig;
  trafficControl: TrafficControlConfig;
  failover: FailoverConfig;
  monitoring: MonitoringConfig;
}

/**
 * 健康检查配置接口
 */
export interface HealthCheckConfig {
  enabled: boolean;
  interval: number; // seconds
  timeout: number; // seconds
  retries: number;
  path: string;
  expectedStatus: number[];
  expectedResponse?: string;
  headers?: Record<string, string>;
}

/**
 * 会话亲和性配置接口
 */
export interface SessionAffinityConfig {
  enabled: boolean;
  type: 'client_ip' | 'cookie' | 'header' | 'jwt';
  cookieName?: string;
  headerName?: string;
  timeout: number; // seconds
  fallbackStrategy: LoadBalancingAlgorithm;
}

/**
 * 流量控制配置接口
 */
export interface TrafficControlConfig {
  rateLimiting: RateLimitConfig;
  circuitBreaker: CircuitBreakerConfig;
  timeout: TimeoutConfig;
  retry: RetryConfig;
  bulkhead: BulkheadConfig;
}

/**
 * 熔断器配置接口
 */
export interface CircuitBreakerConfig {
  enabled: boolean;
  failureThreshold: number;
  recoveryTimeout: number; // seconds
  halfOpenMaxCalls: number;
  monitoringPeriod: number; // seconds
}

/**
 * 超时配置接口
 */
export interface TimeoutConfig {
  connectionTimeout: number; // ms
  requestTimeout: number; // ms
  responseTimeout: number; // ms
}

/**
 * 重试配置接口
 */
export interface RetryConfig {
  enabled: boolean;
  maxAttempts: number;
  backoffStrategy: 'fixed' | 'exponential' | 'linear';
  initialDelay: number; // ms
  maxDelay: number; // ms
  retryableErrors: string[];
}

/**
 * 舱壁配置接口
 */
export interface BulkheadConfig {
  enabled: boolean;
  maxConcurrentRequests: number;
  maxQueueSize: number;
  queueTimeout: number; // ms
}

/**
 * 故障转移配置接口
 */
export interface FailoverConfig {
  enabled: boolean;
  strategy: 'immediate' | 'gradual' | 'manual';
  backupServers: string[];
  failoverThreshold: number;
  recoveryThreshold: number;
  autoRecovery: boolean;
}

/**
 * 监控配置接口
 */
export interface MonitoringConfig {
  metricsEnabled: boolean;
  loggingEnabled: boolean;
  alerting: AlertingConfig;
  dashboard: DashboardConfig;
}

/**
 * 告警配置接口
 */
export interface AlertingConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number; // %
    responseTime: number; // ms
    availability: number; // %
    connectionCount: number;
  };
  channels: AlertChannel[];
}

/**
 * 告警渠道接口
 */
export interface AlertChannel {
  type: 'email' | 'sms' | 'webhook' | 'slack';
  endpoint: string;
  enabled: boolean;
}

/**
 * 仪表板配置接口
 */
export interface DashboardConfig {
  enabled: boolean;
  refreshInterval: number; // seconds
  metrics: string[];
  charts: ChartConfig[];
}

/**
 * 图表配置接口
 */
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'gauge';
  metric: string;
  timeRange: number; // seconds
  aggregation: 'avg' | 'sum' | 'min' | 'max' | 'count';
}

/**
 * 负载均衡请求接口
 */
export interface LoadBalancingRequest {
  requestId: string;
  clientIp: string;
  userAgent?: string;
  headers: Record<string, string>;
  cookies?: Record<string, string>;
  path: string;
  method: string;
  timestamp: Date;
  sessionId?: string;
  userId?: string;
}

/**
 * 负载均衡响应接口
 */
export interface LoadBalancingResponse {
  requestId: string;
  selectedServer: BackendServer;
  algorithm: LoadBalancingAlgorithm;
  selectionTime: number; // ms
  reason: string;
  alternatives?: BackendServer[];
}

@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);

  // 负载均衡器配置
  private loadBalancers = new Map<string, LoadBalancerConfig>();

  // 算法状态
  private roundRobinCounters = new Map<string, number>();
  private consistentHashRings = new Map<string, Map<number, string>>();
  private sessionAffinityMaps = new Map<string, Map<string, string>>();

  // 熔断器状态
  private circuitBreakerStates = new Map<string, {
    state: 'closed' | 'open' | 'half_open';
    failureCount: number;
    lastFailureTime: Date;
    halfOpenCalls: number;
  }>();

  // 统计信息
  private loadBalancingStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageSelectionTime: 0,
    algorithmUsage: new Map<LoadBalancingAlgorithm, number>()
  };

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeDefaultLoadBalancers();
  }

  /**
   * 选择后端服务器
   */
  public async selectServer(
    loadBalancerName: string,
    request: LoadBalancingRequest
  ): Promise<LoadBalancingResponse> {
    try {
      const startTime = Date.now();

      const config = this.loadBalancers.get(loadBalancerName);
      if (!config) {
        throw new Error(`负载均衡器不存在: ${loadBalancerName}`);
      }

      // 获取可用服务器
      const availableServers = this.getAvailableServers(config);

      if (availableServers.length === 0) {
        throw new Error('没有可用的后端服务器');
      }

      // 检查会话亲和性
      let selectedServer = this.checkSessionAffinity(config, request, availableServers);

      if (!selectedServer) {
        // 根据算法选择服务器
        selectedServer = this.selectByAlgorithm(config, request, availableServers);
      }

      const selectionTime = Date.now() - startTime;

      // 更新统计信息
      this.updateLoadBalancingStats(config.algorithm, selectionTime, true);

      // 更新会话亲和性
      this.updateSessionAffinity(config, request, selectedServer);

      const response: LoadBalancingResponse = {
        requestId: request.requestId,
        selectedServer,
        algorithm: config.algorithm,
        selectionTime,
        reason: `选择算法: ${config.algorithm}`,
        alternatives: availableServers.filter(s => s.serverId !== selectedServer.serverId).slice(0, 3)
      };

      this.eventEmitter.emit('server.selected', response);
      this.logger.debug(`服务器选择: ${selectedServer.serverId} (${selectionTime}ms)`);

      return response;

    } catch (error) {
      this.logger.error('服务器选择失败:', error);
      this.updateLoadBalancingStats(LoadBalancingAlgorithm.ROUND_ROBIN, 0, false);
      throw error;
    }
  }

  /**
   * 获取可用服务器
   */
  private getAvailableServers(config: LoadBalancerConfig): BackendServer[] {
    return config.servers.filter(server => {
      // 检查服务器状态
      if (server.status !== ServerStatus.ACTIVE) {
        return false;
      }

      // 检查健康状态
      if (!server.health.isHealthy) {
        return false;
      }

      // 检查熔断器状态
      if (this.isCircuitBreakerOpen(server.serverId)) {
        return false;
      }

      return true;
    });
  }

  /**
   * 检查会话亲和性
   */
  private checkSessionAffinity(
    config: LoadBalancerConfig,
    request: LoadBalancingRequest,
    availableServers: BackendServer[]
  ): BackendServer | null {
    if (!config.sessionAffinity.enabled) {
      return null;
    }

    let affinityKey: string;

    switch (config.sessionAffinity.type) {
      case 'client_ip':
        affinityKey = request.clientIp;
        break;
      case 'cookie':
        affinityKey = request.cookies?.[config.sessionAffinity.cookieName || 'session'] || '';
        break;
      case 'header':
        affinityKey = request.headers[config.sessionAffinity.headerName || 'x-session-id'] || '';
        break;
      default:
        return null;
    }

    if (!affinityKey) {
      return null;
    }

    const affinityMap = this.sessionAffinityMaps.get(config.name) || new Map();
    const serverId = affinityMap.get(affinityKey);

    if (serverId) {
      const server = availableServers.find(s => s.serverId === serverId);
      if (server) {
        return server;
      }
    }

    return null;
  }

  /**
   * 根据算法选择服务器
   */
  private selectByAlgorithm(
    config: LoadBalancerConfig,
    request: LoadBalancingRequest,
    availableServers: BackendServer[]
  ): BackendServer {
    switch (config.algorithm) {
      case LoadBalancingAlgorithm.ROUND_ROBIN:
        return this.roundRobinSelection(config.name, availableServers);
      case LoadBalancingAlgorithm.WEIGHTED_ROUND_ROBIN:
        return this.weightedRoundRobinSelection(availableServers);
      case LoadBalancingAlgorithm.LEAST_CONNECTIONS:
        return this.leastConnectionsSelection(availableServers);
      case LoadBalancingAlgorithm.WEIGHTED_LEAST_CONNECTIONS:
        return this.weightedLeastConnectionsSelection(availableServers);
      case LoadBalancingAlgorithm.IP_HASH:
        return this.ipHashSelection(request.clientIp, availableServers);
      case LoadBalancingAlgorithm.CONSISTENT_HASH:
        return this.consistentHashSelection(config.name, request.clientIp, availableServers);
      case LoadBalancingAlgorithm.LEAST_RESPONSE_TIME:
        return this.leastResponseTimeSelection(availableServers);
      case LoadBalancingAlgorithm.RANDOM:
        return this.randomSelection(availableServers);
      case LoadBalancingAlgorithm.WEIGHTED_RANDOM:
        return this.weightedRandomSelection(availableServers);
      case LoadBalancingAlgorithm.RESOURCE_BASED:
        return this.resourceBasedSelection(availableServers);
      case LoadBalancingAlgorithm.GEOGRAPHIC:
        return this.geographicSelection(request, availableServers);
      case LoadBalancingAlgorithm.AI_OPTIMIZED:
        return this.aiOptimizedSelection(request, availableServers);
      default:
        return this.roundRobinSelection(config.name, availableServers);
    }
  }

  /**
   * 轮询选择
   */
  private roundRobinSelection(configName: string, servers: BackendServer[]): BackendServer {
    const counter = this.roundRobinCounters.get(configName) || 0;
    const selectedIndex = counter % servers.length;
    this.roundRobinCounters.set(configName, counter + 1);
    return servers[selectedIndex];
  }

  /**
   * 加权轮询选择
   */
  private weightedRoundRobinSelection(servers: BackendServer[]): BackendServer {
    const totalWeight = servers.reduce((sum, server) => sum + server.weight, 0);
    const random = Math.random() * totalWeight;

    let currentWeight = 0;
    for (const server of servers) {
      currentWeight += server.weight;
      if (random <= currentWeight) {
        return server;
      }
    }

    return servers[0];
  }

  /**
   * 最少连接选择
   */
  private leastConnectionsSelection(servers: BackendServer[]): BackendServer {
    return servers.reduce((least, current) =>
      current.metrics.activeConnections < least.metrics.activeConnections ? current : least
    );
  }

  /**
   * 加权最少连接选择
   */
  private weightedLeastConnectionsSelection(servers: BackendServer[]): BackendServer {
    return servers.reduce((best, current) => {
      const bestRatio = best.metrics.activeConnections / best.weight;
      const currentRatio = current.metrics.activeConnections / current.weight;
      return currentRatio < bestRatio ? current : best;
    });
  }

  /**
   * IP哈希选择
   */
  private ipHashSelection(clientIp: string, servers: BackendServer[]): BackendServer {
    const hash = this.hashString(clientIp);
    const index = hash % servers.length;
    return servers[index];
  }

  /**
   * 一致性哈希选择
   */
  private consistentHashSelection(configName: string, clientIp: string, servers: BackendServer[]): BackendServer {
    let ring = this.consistentHashRings.get(configName);

    if (!ring) {
      ring = this.buildConsistentHashRing(servers);
      this.consistentHashRings.set(configName, ring);
    }

    const hash = this.hashString(clientIp);
    const sortedHashes = Array.from(ring.keys()).sort((a, b) => a - b);

    for (const ringHash of sortedHashes) {
      if (hash <= ringHash) {
        const serverId = ring.get(ringHash)!;
        return servers.find(s => s.serverId === serverId)!;
      }
    }

    // 如果没有找到，返回第一个
    const firstServerId = ring.get(sortedHashes[0])!;
    return servers.find(s => s.serverId === firstServerId)!;
  }

  /**
   * 最少响应时间选择
   */
  private leastResponseTimeSelection(servers: BackendServer[]): BackendServer {
    return servers.reduce((fastest, current) =>
      current.health.responseTime < fastest.health.responseTime ? current : fastest
    );
  }

  /**
   * 随机选择
   */
  private randomSelection(servers: BackendServer[]): BackendServer {
    const randomIndex = Math.floor(Math.random() * servers.length);
    return servers[randomIndex];
  }

  /**
   * 加权随机选择
   */
  private weightedRandomSelection(servers: BackendServer[]): BackendServer {
    return this.weightedRoundRobinSelection(servers); // 复用加权轮询逻辑
  }

  /**
   * 基于资源的选择
   */
  private resourceBasedSelection(servers: BackendServer[]): BackendServer {
    return servers.reduce((best, current) => {
      const bestScore = this.calculateResourceScore(best);
      const currentScore = this.calculateResourceScore(current);
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * 地理位置选择
   */
  private geographicSelection(request: LoadBalancingRequest, servers: BackendServer[]): BackendServer {
    // 简化实现：优先选择同区域的服务器
    // 实际应该根据客户端IP地理位置计算距离
    return servers[0];
  }

  /**
   * AI优化选择
   */
  private aiOptimizedSelection(request: LoadBalancingRequest, servers: BackendServer[]): BackendServer {
    // 简化实现：综合多个因素的评分
    return servers.reduce((best, current) => {
      const bestScore = this.calculateAIScore(best, request);
      const currentScore = this.calculateAIScore(current, request);
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * 计算资源评分
   */
  private calculateResourceScore(server: BackendServer): number {
    const cpuScore = (100 - server.metrics.cpuUsage) / 100;
    const memoryScore = (100 - server.metrics.memoryUsage) / 100;
    const networkScore = (100 - server.metrics.networkUsage) / 100;
    const connectionScore = Math.max(0, 1 - (server.metrics.activeConnections / server.configuration.maxConnections));

    return (cpuScore + memoryScore + networkScore + connectionScore) / 4;
  }

  /**
   * 计算AI评分
   */
  private calculateAIScore(server: BackendServer, request: LoadBalancingRequest): number {
    let score = 0;

    // 响应时间权重 (30%)
    const responseTimeScore = Math.max(0, 1 - (server.health.responseTime / 1000));
    score += responseTimeScore * 0.3;

    // 资源利用率权重 (25%)
    const resourceScore = this.calculateResourceScore(server);
    score += resourceScore * 0.25;

    // 错误率权重 (20%)
    const errorScore = Math.max(0, 1 - (server.metrics.errorRate / 100));
    score += errorScore * 0.2;

    // 连接数权重 (15%)
    const connectionScore = Math.max(0, 1 - (server.metrics.activeConnections / server.configuration.maxConnections));
    score += connectionScore * 0.15;

    // 健康评分权重 (10%)
    const healthScore = server.health.healthScore / 100;
    score += healthScore * 0.1;

    return score;
  }

  /**
   * 构建一致性哈希环
   */
  private buildConsistentHashRing(servers: BackendServer[]): Map<number, string> {
    const ring = new Map<number, string>();
    const virtualNodes = 150; // 每个服务器的虚拟节点数

    for (const server of servers) {
      for (let i = 0; i < virtualNodes; i++) {
        const hash = this.hashString(`${server.serverId}:${i}`);
        ring.set(hash, server.serverId);
      }
    }

    return ring;
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 检查熔断器是否打开
   */
  private isCircuitBreakerOpen(serverId: string): boolean {
    const state = this.circuitBreakerStates.get(serverId);
    if (!state) {
      return false;
    }

    const now = new Date();

    switch (state.state) {
      case 'open':
        // 检查是否可以进入半开状态
        const timeSinceFailure = now.getTime() - state.lastFailureTime.getTime();
        if (timeSinceFailure > 60000) { // 60秒后尝试半开
          state.state = 'half_open';
          state.halfOpenCalls = 0;
          return false;
        }
        return true;
      case 'half_open':
        return state.halfOpenCalls >= 3; // 半开状态最多3次调用
      default:
        return false;
    }
  }

  /**
   * 更新会话亲和性
   */
  private updateSessionAffinity(
    config: LoadBalancerConfig,
    request: LoadBalancingRequest,
    selectedServer: BackendServer
  ): void {
    if (!config.sessionAffinity.enabled) {
      return;
    }

    let affinityKey: string;

    switch (config.sessionAffinity.type) {
      case 'client_ip':
        affinityKey = request.clientIp;
        break;
      case 'cookie':
        affinityKey = request.cookies?.[config.sessionAffinity.cookieName || 'session'] || '';
        break;
      case 'header':
        affinityKey = request.headers[config.sessionAffinity.headerName || 'x-session-id'] || '';
        break;
      default:
        return;
    }

    if (affinityKey) {
      let affinityMap = this.sessionAffinityMaps.get(config.name);
      if (!affinityMap) {
        affinityMap = new Map();
        this.sessionAffinityMaps.set(config.name, affinityMap);
      }
      affinityMap.set(affinityKey, selectedServer.serverId);
    }
  }

  /**
   * 更新负载均衡统计信息
   */
  private updateLoadBalancingStats(
    algorithm: LoadBalancingAlgorithm,
    selectionTime: number,
    success: boolean
  ): void {
    this.loadBalancingStats.totalRequests++;

    if (success) {
      this.loadBalancingStats.successfulRequests++;
    } else {
      this.loadBalancingStats.failedRequests++;
    }

    // 更新平均选择时间
    this.loadBalancingStats.averageSelectionTime =
      (this.loadBalancingStats.averageSelectionTime * (this.loadBalancingStats.totalRequests - 1) + selectionTime) /
      this.loadBalancingStats.totalRequests;

    // 更新算法使用统计
    const currentCount = this.loadBalancingStats.algorithmUsage.get(algorithm) || 0;
    this.loadBalancingStats.algorithmUsage.set(algorithm, currentCount + 1);
  }

  /**
   * 初始化默认负载均衡器
   */
  private initializeDefaultLoadBalancers(): void {
    // 创建一个默认的负载均衡器配置
    const defaultConfig: LoadBalancerConfig = {
      name: 'default',
      algorithm: LoadBalancingAlgorithm.ROUND_ROBIN,
      servers: [],
      healthCheck: {
        enabled: true,
        interval: 30,
        timeout: 5,
        retries: 3,
        path: '/health',
        expectedStatus: [200]
      },
      sessionAffinity: {
        enabled: false,
        type: 'client_ip',
        timeout: 3600,
        fallbackStrategy: LoadBalancingAlgorithm.ROUND_ROBIN
      },
      trafficControl: {
        rateLimiting: {
          enabled: false,
          requestsPerSecond: 100,
          burstSize: 200,
          windowSize: 60,
          strategy: 'token_bucket'
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 5,
          recoveryTimeout: 60,
          halfOpenMaxCalls: 3,
          monitoringPeriod: 30
        },
        timeout: {
          connectionTimeout: 5000,
          requestTimeout: 30000,
          responseTimeout: 30000
        },
        retry: {
          enabled: true,
          maxAttempts: 3,
          backoffStrategy: 'exponential',
          initialDelay: 1000,
          maxDelay: 10000,
          retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND']
        },
        bulkhead: {
          enabled: false,
          maxConcurrentRequests: 100,
          maxQueueSize: 200,
          queueTimeout: 5000
        }
      },
      failover: {
        enabled: true,
        strategy: 'immediate',
        backupServers: [],
        failoverThreshold: 3,
        recoveryThreshold: 1,
        autoRecovery: true
      },
      monitoring: {
        metricsEnabled: true,
        loggingEnabled: true,
        alerting: {
          enabled: true,
          thresholds: {
            errorRate: 5,
            responseTime: 1000,
            availability: 95,
            connectionCount: 1000
          },
          channels: []
        },
        dashboard: {
          enabled: true,
          refreshInterval: 30,
          metrics: ['response_time', 'error_rate', 'throughput', 'connections'],
          charts: []
        }
      }
    };

    this.loadBalancers.set('default', defaultConfig);
  }

  /**
   * 创建负载均衡器
   */
  public createLoadBalancer(config: LoadBalancerConfig): void {
    this.loadBalancers.set(config.name, config);
    this.logger.log(`负载均衡器已创建: ${config.name}`);
  }

  /**
   * 获取负载均衡器配置
   */
  public getLoadBalancer(name: string): LoadBalancerConfig | undefined {
    return this.loadBalancers.get(name);
  }

  /**
   * 获取所有负载均衡器
   */
  public getAllLoadBalancers(): LoadBalancerConfig[] {
    return Array.from(this.loadBalancers.values());
  }

  /**
   * 添加后端服务器
   */
  public addBackendServer(loadBalancerName: string, server: BackendServer): void {
    const config = this.loadBalancers.get(loadBalancerName);
    if (!config) {
      throw new Error(`负载均衡器不存在: ${loadBalancerName}`);
    }

    config.servers.push(server);

    // 重建一致性哈希环
    if (config.algorithm === LoadBalancingAlgorithm.CONSISTENT_HASH) {
      const ring = this.buildConsistentHashRing(config.servers);
      this.consistentHashRings.set(loadBalancerName, ring);
    }

    this.logger.log(`后端服务器已添加: ${server.serverId} 到 ${loadBalancerName}`);
  }

  /**
   * 移除后端服务器
   */
  public removeBackendServer(loadBalancerName: string, serverId: string): void {
    const config = this.loadBalancers.get(loadBalancerName);
    if (!config) {
      throw new Error(`负载均衡器不存在: ${loadBalancerName}`);
    }

    const index = config.servers.findIndex(s => s.serverId === serverId);
    if (index !== -1) {
      config.servers.splice(index, 1);

      // 重建一致性哈希环
      if (config.algorithm === LoadBalancingAlgorithm.CONSISTENT_HASH) {
        const ring = this.buildConsistentHashRing(config.servers);
        this.consistentHashRings.set(loadBalancerName, ring);
      }

      // 清理相关状态
      this.circuitBreakerStates.delete(serverId);

      this.logger.log(`后端服务器已移除: ${serverId} 从 ${loadBalancerName}`);
    }
  }

  /**
   * 获取负载均衡统计信息
   */
  public getLoadBalancingStats(): any {
    return {
      ...this.loadBalancingStats,
      successRate: this.loadBalancingStats.totalRequests > 0 ?
        (this.loadBalancingStats.successfulRequests / this.loadBalancingStats.totalRequests) * 100 : 0,
      algorithmUsage: Object.fromEntries(this.loadBalancingStats.algorithmUsage)
    };
  }

  /**
   * 报告服务器故障
   */
  public reportServerFailure(serverId: string): void {
    let state = this.circuitBreakerStates.get(serverId);

    if (!state) {
      state = {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: new Date(),
        halfOpenCalls: 0
      };
      this.circuitBreakerStates.set(serverId, state);
    }

    state.failureCount++;
    state.lastFailureTime = new Date();

    // 检查是否需要打开熔断器
    if (state.failureCount >= 5) { // 失败阈值
      state.state = 'open';
      this.logger.warn(`熔断器已打开: ${serverId}`);
      this.eventEmitter.emit('circuit_breaker.opened', { serverId });
    }
  }

  /**
   * 报告服务器成功
   */
  public reportServerSuccess(serverId: string): void {
    const state = this.circuitBreakerStates.get(serverId);

    if (state) {
      if (state.state === 'half_open') {
        state.halfOpenCalls++;

        // 如果半开状态下成功调用足够多，关闭熔断器
        if (state.halfOpenCalls >= 3) {
          state.state = 'closed';
          state.failureCount = 0;
          this.logger.log(`熔断器已关闭: ${serverId}`);
          this.eventEmitter.emit('circuit_breaker.closed', { serverId });
        }
      } else if (state.state === 'closed') {
        // 重置失败计数
        state.failureCount = Math.max(0, state.failureCount - 1);
      }
    }
  }
}