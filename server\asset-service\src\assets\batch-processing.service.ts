/**
 * 批量处理服务
 * 提供批量上传、转换、优化等功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';

/**
 * 批量任务状态枚举
 */
export enum BatchTaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

/**
 * 批量任务类型枚举
 */
export enum BatchTaskType {
  UPLOAD = 'upload',
  CONVERT = 'convert',
  OPTIMIZE = 'optimize',
  COMPRESS = 'compress',
  RESIZE = 'resize',
  WATERMARK = 'watermark',
  METADATA_EXTRACT = 'metadata_extract',
  THUMBNAIL_GENERATE = 'thumbnail_generate',
  BACKUP = 'backup',
  MIGRATION = 'migration'
}

/**
 * 批量任务接口
 */
export interface BatchTask {
  id: string;
  type: BatchTaskType;
  name: string;
  description: string;
  status: BatchTaskStatus;
  priority: number;
  createdBy: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: {
    total: number;
    completed: number;
    failed: number;
    percentage: number;
  };
  configuration: BatchTaskConfig;
  results: BatchTaskResult[];
  errors: BatchTaskError[];
  metadata: Record<string, any>;
}

/**
 * 批量任务配置接口
 */
export interface BatchTaskConfig {
  concurrency: number; // 并发数
  retryAttempts: number; // 重试次数
  retryDelay: number; // 重试延迟（毫秒）
  timeout: number; // 超时时间（毫秒）
  batchSize: number; // 批次大小
  pauseOnError: boolean; // 出错时暂停
  notifyOnCompletion: boolean; // 完成时通知
  outputFormat?: string; // 输出格式
  quality?: number; // 质量设置
  dimensions?: { width: number; height: number }; // 尺寸设置
  filters?: string[]; // 过滤器
  customOptions?: Record<string, any>; // 自定义选项
}

/**
 * 批量任务项接口
 */
export interface BatchTaskItem {
  id: string;
  taskId: string;
  sourceUrl: string;
  targetUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  metadata: Record<string, any>;
}

/**
 * 批量任务结果接口
 */
export interface BatchTaskResult {
  itemId: string;
  success: boolean;
  sourceUrl: string;
  targetUrl?: string;
  processingTime: number;
  fileSize: number;
  metadata: Record<string, any>;
  error?: string;
}

/**
 * 批量任务错误接口
 */
export interface BatchTaskError {
  itemId: string;
  error: string;
  timestamp: Date;
  retryCount: number;
  fatal: boolean;
}

/**
 * 处理器接口
 */
export interface BatchProcessor {
  type: BatchTaskType;
  process(item: BatchTaskItem, config: BatchTaskConfig): Promise<BatchTaskResult>;
  validate(config: BatchTaskConfig): boolean;
  estimateTime(items: BatchTaskItem[], config: BatchTaskConfig): number;
}

@Injectable()
export class BatchProcessingService {
  private readonly logger = new Logger(BatchProcessingService.name);
  
  // 任务存储
  private tasks = new Map<string, BatchTask>();
  private taskItems = new Map<string, BatchTaskItem[]>();
  private runningTasks = new Set<string>();
  
  // 处理器注册
  private processors = new Map<BatchTaskType, BatchProcessor>();
  
  // 队列管理
  private taskQueue: string[] = [];
  private maxConcurrentTasks = 3;
  private currentRunningTasks = 0;

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.registerDefaultProcessors();
    this.startTaskProcessor();
  }

  /**
   * 创建批量任务
   */
  public async createBatchTask(
    type: BatchTaskType,
    name: string,
    items: Array<{ sourceUrl: string; metadata?: Record<string, any> }>,
    config: Partial<BatchTaskConfig>,
    createdBy: string,
    options: {
      description?: string;
      priority?: number;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<BatchTask> {
    try {
      const taskId = uuidv4();
      
      // 创建任务项
      const taskItems: BatchTaskItem[] = items.map(item => ({
        id: uuidv4(),
        taskId,
        sourceUrl: item.sourceUrl,
        status: 'pending',
        progress: 0,
        metadata: item.metadata || {}
      }));
      
      // 创建任务
      const task: BatchTask = {
        id: taskId,
        type,
        name,
        description: options.description || '',
        status: BatchTaskStatus.PENDING,
        priority: options.priority || 5,
        createdBy,
        createdAt: new Date(),
        progress: {
          total: taskItems.length,
          completed: 0,
          failed: 0,
          percentage: 0
        },
        configuration: {
          concurrency: 3,
          retryAttempts: 3,
          retryDelay: 1000,
          timeout: 30000,
          batchSize: 10,
          pauseOnError: false,
          notifyOnCompletion: true,
          ...config
        },
        results: [],
        errors: [],
        metadata: options.metadata || {}
      };
      
      // 验证配置
      const processor = this.processors.get(type);
      if (!processor) {
        throw new Error(`不支持的任务类型: ${type}`);
      }
      
      if (!processor.validate(task.configuration)) {
        throw new Error('任务配置验证失败');
      }
      
      // 存储任务和任务项
      this.tasks.set(taskId, task);
      this.taskItems.set(taskId, taskItems);
      
      // 添加到队列
      this.taskQueue.push(taskId);
      
      // 发送事件
      this.eventEmitter.emit('batch.task.created', {
        task,
        itemCount: taskItems.length
      });
      
      this.logger.log(`批量任务已创建: ${name} (${taskItems.length} 项)`);
      return task;
      
    } catch (error) {
      this.logger.error('创建批量任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务详情
   */
  public getTask(taskId: string): BatchTask | null {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 获取任务列表
   */
  public getTasks(
    filters: {
      status?: BatchTaskStatus;
      type?: BatchTaskType;
      createdBy?: string;
    } = {},
    pagination: {
      page: number;
      limit: number;
    } = { page: 1, limit: 20 }
  ): {
    tasks: BatchTask[];
    total: number;
    page: number;
    limit: number;
  } {
    let tasks = Array.from(this.tasks.values());
    
    // 应用过滤器
    if (filters.status) {
      tasks = tasks.filter(task => task.status === filters.status);
    }
    if (filters.type) {
      tasks = tasks.filter(task => task.type === filters.type);
    }
    if (filters.createdBy) {
      tasks = tasks.filter(task => task.createdBy === filters.createdBy);
    }
    
    // 排序
    tasks.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // 高优先级在前
      }
      return b.createdAt.getTime() - a.createdAt.getTime(); // 新任务在前
    });
    
    // 分页
    const total = tasks.length;
    const start = (pagination.page - 1) * pagination.limit;
    const paginatedTasks = tasks.slice(start, start + pagination.limit);
    
    return {
      tasks: paginatedTasks,
      total,
      page: pagination.page,
      limit: pagination.limit
    };
  }

  /**
   * 暂停任务
   */
  public async pauseTask(taskId: string): Promise<boolean> {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        return false;
      }
      
      if (task.status === BatchTaskStatus.RUNNING) {
        task.status = BatchTaskStatus.PAUSED;
        this.runningTasks.delete(taskId);
        
        this.eventEmitter.emit('batch.task.paused', { task });
        this.logger.log(`任务已暂停: ${task.name}`);
      }
      
      return true;
      
    } catch (error) {
      this.logger.error('暂停任务失败:', error);
      return false;
    }
  }

  /**
   * 恢复任务
   */
  public async resumeTask(taskId: string): Promise<boolean> {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        return false;
      }
      
      if (task.status === BatchTaskStatus.PAUSED) {
        task.status = BatchTaskStatus.PENDING;
        
        // 重新添加到队列
        if (!this.taskQueue.includes(taskId)) {
          this.taskQueue.unshift(taskId); // 优先处理
        }
        
        this.eventEmitter.emit('batch.task.resumed', { task });
        this.logger.log(`任务已恢复: ${task.name}`);
      }
      
      return true;
      
    } catch (error) {
      this.logger.error('恢复任务失败:', error);
      return false;
    }
  }

  /**
   * 取消任务
   */
  public async cancelTask(taskId: string): Promise<boolean> {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        return false;
      }
      
      task.status = BatchTaskStatus.CANCELLED;
      task.completedAt = new Date();
      
      // 从队列和运行中移除
      const queueIndex = this.taskQueue.indexOf(taskId);
      if (queueIndex !== -1) {
        this.taskQueue.splice(queueIndex, 1);
      }
      this.runningTasks.delete(taskId);
      
      this.eventEmitter.emit('batch.task.cancelled', { task });
      this.logger.log(`任务已取消: ${task.name}`);
      
      return true;
      
    } catch (error) {
      this.logger.error('取消任务失败:', error);
      return false;
    }
  }

  /**
   * 获取任务统计
   */
  public getTaskStatistics(): {
    total: number;
    byStatus: Record<BatchTaskStatus, number>;
    byType: Record<BatchTaskType, number>;
    averageProcessingTime: number;
    successRate: number;
  } {
    const tasks = Array.from(this.tasks.values());
    
    const byStatus = {} as Record<BatchTaskStatus, number>;
    const byType = {} as Record<BatchTaskType, number>;
    
    let totalProcessingTime = 0;
    let completedTasks = 0;
    let successfulTasks = 0;
    
    for (const task of tasks) {
      // 按状态统计
      byStatus[task.status] = (byStatus[task.status] || 0) + 1;
      
      // 按类型统计
      byType[task.type] = (byType[task.type] || 0) + 1;
      
      // 处理时间统计
      if (task.startedAt && task.completedAt) {
        totalProcessingTime += task.completedAt.getTime() - task.startedAt.getTime();
        completedTasks++;
        
        if (task.status === BatchTaskStatus.COMPLETED) {
          successfulTasks++;
        }
      }
    }
    
    return {
      total: tasks.length,
      byStatus,
      byType,
      averageProcessingTime: completedTasks > 0 ? totalProcessingTime / completedTasks : 0,
      successRate: completedTasks > 0 ? (successfulTasks / completedTasks) * 100 : 0
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 注册默认处理器
   */
  private registerDefaultProcessors(): void {
    // 上传处理器
    this.processors.set(BatchTaskType.UPLOAD, {
      type: BatchTaskType.UPLOAD,
      process: async (item, config) => {
        const startTime = Date.now();
        
        // 模拟上传处理
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return {
          itemId: item.id,
          success: true,
          sourceUrl: item.sourceUrl,
          targetUrl: `https://cdn.example.com/${path.basename(item.sourceUrl)}`,
          processingTime: Date.now() - startTime,
          fileSize: 1024 * 1024, // 1MB
          metadata: {}
        };
      },
      validate: (config) => config.concurrency > 0 && config.concurrency <= 10,
      estimateTime: (items, config) => items.length * 1000 / config.concurrency
    });

    // 转换处理器
    this.processors.set(BatchTaskType.CONVERT, {
      type: BatchTaskType.CONVERT,
      process: async (item, config) => {
        const startTime = Date.now();
        
        // 模拟转换处理
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        return {
          itemId: item.id,
          success: true,
          sourceUrl: item.sourceUrl,
          targetUrl: item.sourceUrl.replace(/\.[^.]+$/, `.${config.outputFormat || 'jpg'}`),
          processingTime: Date.now() - startTime,
          fileSize: 1024 * 1024,
          metadata: { format: config.outputFormat }
        };
      },
      validate: (config) => !!config.outputFormat,
      estimateTime: (items, config) => items.length * 2000 / config.concurrency
    });

    // 优化处理器
    this.processors.set(BatchTaskType.OPTIMIZE, {
      type: BatchTaskType.OPTIMIZE,
      process: async (item, config) => {
        const startTime = Date.now();
        
        // 模拟优化处理
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        return {
          itemId: item.id,
          success: true,
          sourceUrl: item.sourceUrl,
          targetUrl: item.sourceUrl,
          processingTime: Date.now() - startTime,
          fileSize: 1024 * 1024 * 0.8, // 优化后减少20%
          metadata: { optimized: true, quality: config.quality }
        };
      },
      validate: (config) => config.quality >= 1 && config.quality <= 100,
      estimateTime: (items, config) => items.length * 1500 / config.concurrency
    });
  }

  /**
   * 启动任务处理器
   */
  private startTaskProcessor(): void {
    setInterval(async () => {
      if (this.currentRunningTasks >= this.maxConcurrentTasks || this.taskQueue.length === 0) {
        return;
      }
      
      const taskId = this.taskQueue.shift();
      if (!taskId) return;
      
      const task = this.tasks.get(taskId);
      if (!task || task.status !== BatchTaskStatus.PENDING) {
        return;
      }
      
      this.currentRunningTasks++;
      this.runningTasks.add(taskId);
      
      try {
        await this.processTask(task);
      } catch (error) {
        this.logger.error(`任务处理失败: ${task.name}`, error);
      } finally {
        this.currentRunningTasks--;
        this.runningTasks.delete(taskId);
      }
    }, 1000);
  }

  /**
   * 处理任务
   */
  private async processTask(task: BatchTask): Promise<void> {
    try {
      task.status = BatchTaskStatus.RUNNING;
      task.startedAt = new Date();
      
      const items = this.taskItems.get(task.id) || [];
      const processor = this.processors.get(task.type);
      
      if (!processor) {
        throw new Error(`处理器不存在: ${task.type}`);
      }
      
      this.eventEmitter.emit('batch.task.started', { task });
      
      // 分批处理
      const batchSize = task.configuration.batchSize;
      const concurrency = task.configuration.concurrency;
      
      for (let i = 0; i < items.length; i += batchSize) {
        if (task.status === BatchTaskStatus.PAUSED || task.status === BatchTaskStatus.CANCELLED) {
          break;
        }
        
        const batch = items.slice(i, i + batchSize);
        await this.processBatch(batch, processor, task);
      }
      
      // 完成任务
      if (task.status === BatchTaskStatus.RUNNING) {
        task.status = BatchTaskStatus.COMPLETED;
        task.completedAt = new Date();
        
        this.eventEmitter.emit('batch.task.completed', { task });
        this.logger.log(`任务完成: ${task.name}`);
      }
      
    } catch (error) {
      task.status = BatchTaskStatus.FAILED;
      task.completedAt = new Date();
      
      this.eventEmitter.emit('batch.task.failed', { task, error });
      this.logger.error(`任务失败: ${task.name}`, error);
    }
  }

  /**
   * 处理批次
   */
  private async processBatch(
    items: BatchTaskItem[],
    processor: BatchProcessor,
    task: BatchTask
  ): Promise<void> {
    const promises = items.map(async (item) => {
      if (task.status !== BatchTaskStatus.RUNNING) {
        return;
      }
      
      try {
        item.status = 'processing';
        item.startedAt = new Date();
        
        const result = await processor.process(item, task.configuration);
        
        item.status = 'completed';
        item.completedAt = new Date();
        item.progress = 100;
        
        task.results.push(result);
        task.progress.completed++;
        
      } catch (error) {
        item.status = 'failed';
        item.completedAt = new Date();
        item.error = error.message;
        
        task.errors.push({
          itemId: item.id,
          error: error.message,
          timestamp: new Date(),
          retryCount: 0,
          fatal: false
        });
        
        task.progress.failed++;
        
        if (task.configuration.pauseOnError) {
          task.status = BatchTaskStatus.PAUSED;
        }
      }
      
      // 更新进度
      task.progress.percentage = 
        ((task.progress.completed + task.progress.failed) / task.progress.total) * 100;
      
      this.eventEmitter.emit('batch.task.progress', {
        taskId: task.id,
        progress: task.progress
      });
    });
    
    await Promise.all(promises);
  }
}
