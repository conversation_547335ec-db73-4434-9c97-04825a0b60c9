import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { RoomService } from './room.service';
import { RoomEvent } from './room.interface';

@WebSocketGateway({
  namespace: '/rooms',
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
})
export class RoomGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RoomGateway.name);
  private readonly clientRoomMap = new Map<string, string>(); // socketId -> roomId
  private readonly clientPlayerMap = new Map<string, string>(); // socketId -> playerId

  constructor(private readonly roomService: RoomService) {}

  afterInit(server: Server) {
    this.logger.log('房间WebSocket网关已初始化');
  }

  handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`);
  }

  async handleDisconnect(client: Socket) {
    this.logger.log(`客户端断开连接: ${client.id}`);
    
    // 处理玩家离开房间
    const playerId = this.clientPlayerMap.get(client.id);
    if (playerId) {
      await this.roomService.leaveRoom(playerId);
      this.clientPlayerMap.delete(client.id);
    }
    
    this.clientRoomMap.delete(client.id);
  }

  /**
   * 加入房间频道
   */
  @SubscribeMessage('join-room-channel')
  async handleJoinRoomChannel(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { roomId: string; playerId: string }
  ) {
    try {
      const { roomId, playerId } = data;
      
      // 离开之前的房间频道
      const previousRoomId = this.clientRoomMap.get(client.id);
      if (previousRoomId) {
        client.leave(`room:${previousRoomId}`);
      }
      
      // 加入新的房间频道
      client.join(`room:${roomId}`);
      this.clientRoomMap.set(client.id, roomId);
      this.clientPlayerMap.set(client.id, playerId);
      
      // 获取房间信息并发送给客户端
      const room = this.roomService.getRoom(roomId);
      if (room) {
        client.emit('room-info', { room });
      }
      
      this.logger.log(`客户端 ${client.id} 加入房间频道 ${roomId}`);
    } catch (error) {
      this.logger.error(`加入房间频道失败: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 离开房间频道
   */
  @SubscribeMessage('leave-room-channel')
  handleLeaveRoomChannel(@ConnectedSocket() client: Socket) {
    const roomId = this.clientRoomMap.get(client.id);
    if (roomId) {
      client.leave(`room:${roomId}`);
      this.clientRoomMap.delete(client.id);
    }
    
    this.clientPlayerMap.delete(client.id);
    this.logger.log(`客户端 ${client.id} 离开房间频道`);
  }

  /**
   * 发送房间消息
   */
  @SubscribeMessage('room-message')
  handleRoomMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { message: string; type?: string }
  ) {
    const roomId = this.clientRoomMap.get(client.id);
    const playerId = this.clientPlayerMap.get(client.id);
    
    if (!roomId || !playerId) {
      client.emit('error', { message: '未加入房间' });
      return;
    }
    
    const messageData = {
      playerId,
      message: data.message,
      type: data.type || 'chat',
      timestamp: new Date()
    };
    
    // 广播消息到房间内所有客户端
    this.server.to(`room:${roomId}`).emit('room-message', messageData);
    
    this.logger.log(`房间 ${roomId} 收到消息: ${data.message}`);
  }

  /**
   * 更新玩家状态
   */
  @SubscribeMessage('update-player-status')
  handleUpdatePlayerStatus(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { status: string; metadata?: any }
  ) {
    const roomId = this.clientRoomMap.get(client.id);
    const playerId = this.clientPlayerMap.get(client.id);
    
    if (!roomId || !playerId) {
      client.emit('error', { message: '未加入房间' });
      return;
    }
    
    const statusData = {
      playerId,
      status: data.status,
      metadata: data.metadata,
      timestamp: new Date()
    };
    
    // 广播状态更新到房间内所有客户端
    this.server.to(`room:${roomId}`).emit('player-status-updated', statusData);
  }

  /**
   * 同步游戏状态
   */
  @SubscribeMessage('sync-game-state')
  handleSyncGameState(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { gameState: any; timestamp?: number }
  ) {
    const roomId = this.clientRoomMap.get(client.id);
    const playerId = this.clientPlayerMap.get(client.id);
    
    if (!roomId || !playerId) {
      client.emit('error', { message: '未加入房间' });
      return;
    }
    
    const syncData = {
      playerId,
      gameState: data.gameState,
      timestamp: data.timestamp || Date.now()
    };
    
    // 广播游戏状态到房间内其他客户端
    client.to(`room:${roomId}`).emit('game-state-synced', syncData);
  }

  /**
   * 心跳检测
   */
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', { timestamp: Date.now() });
  }

  // 事件监听器

  /**
   * 监听房间创建事件
   */
  @OnEvent('room.created')
  handleRoomCreated(event: RoomEvent) {
    this.server.emit('room-created', {
      roomId: event.roomId,
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听玩家加入事件
   */
  @OnEvent('player.joined')
  handlePlayerJoined(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('player-joined', {
      playerId: event.playerId,
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听玩家离开事件
   */
  @OnEvent('player.left')
  handlePlayerLeft(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('player-left', {
      playerId: event.playerId,
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听游戏开始事件
   */
  @OnEvent('game.starting')
  handleGameStarting(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('game-starting', {
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听游戏开始事件
   */
  @OnEvent('game.started')
  handleGameStarted(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('game-started', {
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听房主变更事件
   */
  @OnEvent('host.changed')
  handleHostChanged(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('host-changed', {
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听房间关闭事件
   */
  @OnEvent('room.closed')
  handleRoomClosed(event: RoomEvent) {
    this.server.to(`room:${event.roomId}`).emit('room-closed', {
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 向特定房间发送消息
   */
  sendToRoom(roomId: string, event: string, data: any) {
    this.server.to(`room:${roomId}`).emit(event, data);
  }

  /**
   * 向特定玩家发送消息
   */
  sendToPlayer(playerId: string, event: string, data: any) {
    // 查找玩家的socket连接
    for (const [socketId, playerIdInMap] of this.clientPlayerMap.entries()) {
      if (playerIdInMap === playerId) {
        this.server.to(socketId).emit(event, data);
        break;
      }
    }
  }
}
