/**
 * 项目模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ProjectsController } from './projects.controller';
import { CollaborationController } from './collaboration.controller';
import { VersionControlController } from './version-control.controller';
import { ProjectsService } from './projects.service';
import { CollaborationService } from './collaboration.service';
import { VersionControlService } from './version-control.service';
import { PermissionService } from './permission.service';
import { Project } from './entities/project.entity';
import { ProjectMember } from './entities/project-member.entity';
import { ProjectSetting } from './entities/project-setting.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Project, ProjectMember, ProjectSetting]),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [
    ProjectsController,
    CollaborationController,
    VersionControlController
  ],
  providers: [
    ProjectsService,
    CollaborationService,
    VersionControlService,
    PermissionService
  ],
  exports: [
    ProjectsService,
    CollaborationService,
    VersionControlService,
    PermissionService
  ],
})
export class ProjectsModule {}
